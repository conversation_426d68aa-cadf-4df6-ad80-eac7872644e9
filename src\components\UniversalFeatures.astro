---
// Universal Features component
// Displays common features that apply to all packages
// Reduces redundancy in individual package descriptions

export interface Props {
  class?: string;
  variant?: 'default' | 'compact' | 'minimal';
}

const { 
  class: className = '',
  variant = 'default'
} = Astro.props;

// Universal features that apply to all packages
const universalFeatures = [
  {
    icon: '🎨',
    title: 'Design',
    description: 'Expert arrangement and styling for maximum impact'
  },
  {
    icon: '🚚',
    title: 'Delivery',
    description: 'Complete delivery to your location included'
  },
  {
    icon: '🔧',
    title: 'Setup',
    description: 'Professional installation and arrangement'
  },
  {
    icon: '✅',
    title: 'Freshness Guarantee',
    description: 'Quality guarantee for the entire display period'
  }
];

const getVariantClasses = () => {
  switch (variant) {
    case 'compact':
      return 'py-8';
    case 'minimal':
      return 'py-6';
    default:
      return 'py-12';
  }
};
---

<section class:list={[
  'bg-gradient-to-r from-primary-50 to-secondary-50',
  getVariantClasses(),
  className
]}>
  <div class="container mx-auto px-4">
    <div class="text-center mb-8">
      <h3 class="text-2xl md:text-3xl font-serif font-bold text-gray-900 mb-3">
        Every Package Includes
      </h3>
      <p class="text-gray-600 max-w-2xl mx-auto">
        All Mountain Porch Pumpkins packages come with these essential services at no extra cost
      </p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-6xl mx-auto">
      {universalFeatures.map((feature) => (
        <div class="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow duration-300 text-center">
          <div class="text-4xl mb-4">{feature.icon}</div>
          <h4 class="font-semibold text-gray-900 mb-2">{feature.title}</h4>
          <p class="text-sm text-gray-600 leading-relaxed">{feature.description}</p>
        </div>
      ))}
    </div>


  </div>
</section>

<style>
  /* Ensure consistent spacing and alignment */
  .container {
    max-width: 1200px;
  }
  
  /* Hover effects for better interactivity */
  .bg-white:hover {
    transform: translateY(-2px);
  }
  
  /* Mobile optimizations */
  @media (max-width: 768px) {
    .grid {
      grid-template-columns: 1fr;
      gap: 1rem;
    }
    
    .text-4xl {
      font-size: 2.5rem;
    }
  }
</style>
