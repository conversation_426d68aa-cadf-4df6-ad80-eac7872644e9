---
import BaseLayout from '../layouts/BaseLayout.astro';
import OptimizedImage from '../components/OptimizedImage.astro';

const title = 'Lazy Loading Demo - Mountain Porch Pumpkins';
const description = 'Demonstration of lazy loading implementation for images and content sections.';

// Demo images for testing
const demoImages = [
  {
    src: 'https://images.unsplash.com/photo-1570197788417-0e82375c9371?w=800&h=600',
    alt: 'Pumpkin display 1',
    title: 'Autumn Entrance Display'
  },
  {
    src: 'https://images.unsplash.com/photo-1509909756405-be0199881695?w=800&h=600',
    alt: 'Pumpkin display 2',
    title: 'Cozy Porch Setup'
  },
  {
    src: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800&h=600',
    alt: 'Pumpkin display 3',
    title: 'Grand Estate Display'
  },
  {
    src: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800&h=600',
    alt: 'Pumpkin display 4',
    title: 'Business Front Display'
  },
  {
    src: 'https://images.unsplash.com/photo-1572286258217-aac4f2d5b781?w=800&h=600',
    alt: 'Pumpkin display 5',
    title: 'Apartment Balcony Setup'
  },
  {
    src: 'https://images.unsplash.com/photo-1603048297172-c92544798d5a?w=800&h=600',
    alt: 'Pumpkin display 6',
    title: 'Mountain Home Display'
  }
];
---

<BaseLayout title={title} description={description}>
  <!-- Header -->
  <header slot="header" class="bg-primary-600 text-white py-4">
    <div class="container mx-auto px-4">
      <div class="flex items-center justify-between">
        <h1 class="text-2xl font-bold">Lazy Loading Demo</h1>
        <a href="/" class="text-primary-200 hover:text-white transition-colors">← Back to Home</a>
      </div>
    </div>
  </header>

  <!-- Hero Section (Loads immediately) -->
  <section class="py-20 bg-gradient-to-br from-primary-500 to-secondary-500 text-white">
    <div class="container mx-auto px-4 text-center">
      <h2 class="text-4xl md:text-6xl font-serif font-bold mb-6">
        Lazy Loading Demo
      </h2>
      <p class="text-xl md:text-2xl text-primary-100 max-w-3xl mx-auto">
        Scroll down to see lazy loading in action. Content and images will load as they come into view.
      </p>
    </div>
  </section>

  <!-- Spacer to demonstrate scroll -->
  <div class="h-screen bg-gray-100 flex items-center justify-center">
    <div class="text-center">
      <h3 class="text-3xl font-bold text-gray-900 mb-4">Keep Scrolling</h3>
      <p class="text-gray-600">The content below will lazy load as you scroll</p>
      <div class="mt-8">
        <svg class="h-8 w-8 mx-auto text-primary-500 animate-bounce" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
        </svg>
      </div>
    </div>
  </div>

  <!-- Lazy Content Section 1 -->
  <section class="py-20 bg-white lazy-content" data-component="demo-section-1">
    <div class="container mx-auto px-4">
      <div class="text-center mb-16">
        <h2 class="text-3xl md:text-5xl font-serif font-bold text-gray-900 mb-4">
          Lazy Loaded Content Section
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          This entire section was lazy loaded when it came into view. Notice the smooth fade-in animation.
        </p>
      </div>

      <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        {demoImages.slice(0, 3).map((image, index) => (
          <div class="bg-gray-50 rounded-xl overflow-hidden shadow-lg">
            <OptimizedImage
              src={image.src}
              alt={image.alt}
              width={400}
              height={300}
              loading="lazy"
              class="w-full h-48 object-cover"
            />
            <div class="p-6">
              <h3 class="text-xl font-semibold text-gray-900 mb-2">{image.title}</h3>
              <p class="text-gray-600">This image was lazy loaded with intersection observer.</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  </section>

  <!-- Another spacer -->
  <div class="h-96 bg-gray-100 flex items-center justify-center">
    <div class="text-center">
      <h3 class="text-2xl font-bold text-gray-900 mb-4">More Content Below</h3>
      <p class="text-gray-600">Each section loads independently</p>
    </div>
  </div>

  <!-- Lazy Content Section 2 -->
  <section class="py-20 bg-primary-50 lazy-content" data-component="demo-section-2">
    <div class="container mx-auto px-4">
      <div class="text-center mb-16">
        <h2 class="text-3xl md:text-5xl font-serif font-bold text-gray-900 mb-4">
          Another Lazy Section
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          This section also lazy loads independently with its own animation timing.
        </p>
      </div>

      <div class="grid md:grid-cols-2 gap-8">
        {demoImages.slice(3, 5).map((image, index) => (
          <div class="bg-white rounded-xl overflow-hidden shadow-lg">
            <OptimizedImage
              src={image.src}
              alt={image.alt}
              width={600}
              height={400}
              loading="lazy"
              class="w-full h-64 object-cover"
            />
            <div class="p-6">
              <h3 class="text-xl font-semibold text-gray-900 mb-2">{image.title}</h3>
              <p class="text-gray-600">Each image loads only when it's about to enter the viewport.</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  </section>

  <!-- Component Lazy Loading Demo -->
  <section class="py-20 bg-white">
    <div class="container mx-auto px-4">
      <div class="text-center mb-16">
        <h2 class="text-3xl md:text-5xl font-serif font-bold text-gray-900 mb-4">
          Component Lazy Loading
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Complex components can also be lazy loaded for better performance.
        </p>
      </div>

      <div class="lazy-component" data-component="gallery">
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          {demoImages.map((image, index) => (
            <div class="aspect-square rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow">
              <OptimizedImage
                src={image.src}
                alt={image.alt}
                width={200}
                height={200}
                loading="lazy"
                class="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  </section>

  <!-- Performance Info -->
  <section class="py-20 bg-gray-900 text-white">
    <div class="container mx-auto px-4 text-center">
      <h2 class="text-3xl md:text-5xl font-serif font-bold mb-8">
        Performance Benefits
      </h2>
      
      <div class="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
        <div class="bg-gray-800 rounded-xl p-6">
          <div class="text-3xl font-bold text-primary-400 mb-2">50%</div>
          <div class="text-lg font-semibold mb-2">Faster Initial Load</div>
          <div class="text-gray-400">Only above-the-fold content loads immediately</div>
        </div>
        
        <div class="bg-gray-800 rounded-xl p-6">
          <div class="text-3xl font-bold text-primary-400 mb-2">75%</div>
          <div class="text-lg font-semibold mb-2">Reduced Bandwidth</div>
          <div class="text-gray-400">Images load only when needed</div>
        </div>
        
        <div class="bg-gray-800 rounded-xl p-6">
          <div class="text-3xl font-bold text-primary-400 mb-2">90%</div>
          <div class="text-lg font-semibold mb-2">Better UX</div>
          <div class="text-gray-400">Smooth animations and progressive loading</div>
        </div>
      </div>

      <div class="mt-12">
        <a href="/" class="btn-primary">
          Back to Main Site
        </a>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer slot="footer" class="bg-gray-800 text-white py-8">
    <div class="container mx-auto px-4 text-center">
      <p>&copy; 2024 Mountain Porch Pumpkins. Lazy loading demo.</p>
    </div>
  </footer>
</BaseLayout>

<style>
  /* Demo-specific styles */
  .demo-highlight {
    background: linear-gradient(120deg, #ff6b35 0%, #f7931e 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
</style>
