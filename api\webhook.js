const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    res.setHeader('Allow', 'POST');
    res.status(405).end('Method Not Allowed');
    return;
  }

  const sig = req.headers['stripe-signature'];
  const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;

  let event;

  try {
    event = stripe.webhooks.constructEvent(req.body, sig, endpointSecret);
  } catch (err) {
    console.error('Webhook signature verification failed:', err.message);
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  // Handle the event
  switch (event.type) {
    case 'payment_intent.succeeded':
      const paymentIntent = event.data.object;
      console.log('Payment succeeded:', paymentIntent.id);
      
      // Here you could:
      // - Send confirmation email to customer
      // - Add to your order management system
      // - Send notification to your team
      
      await handleSuccessfulPayment(paymentIntent);
      break;
      
    case 'payment_intent.payment_failed':
      const failedPayment = event.data.object;
      console.log('Payment failed:', failedPayment.id);
      
      await handleFailedPayment(failedPayment);
      break;
      
    default:
      console.log(`Unhandled event type ${event.type}`);
  }

  res.status(200).json({ received: true });
}

async function handleSuccessfulPayment(paymentIntent) {
  // Extract customer info from metadata
  const metadata = paymentIntent.metadata;
  
  // Log successful order
  console.log('New order received:', {
    paymentId: paymentIntent.id,
    amount: paymentIntent.amount / 100,
    customer: metadata.customer_name,
    email: metadata.customer_email,
    package: metadata.package_selection,
    deliveryDate: metadata.delivery_date
  });
  
  // Here you could integrate with:
  // - Email service (SendGrid, Mailgun)
  // - CRM system
  // - Order management system
  // - SMS notifications
}

async function handleFailedPayment(paymentIntent) {
  console.log('Payment failed for:', paymentIntent.metadata.customer_email);
  
  // Handle failed payment
  // - Send notification to customer
  // - Log for follow-up
}
