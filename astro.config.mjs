// @ts-check
import { defineConfig } from 'astro/config';
import tailwind from '@astrojs/tailwind';
import alpinejs from '@astrojs/alpinejs';
import node from '@astrojs/node';

// https://astro.build/config
export default defineConfig({
  output: 'server',
  adapter: node({
    mode: 'standalone'
  }),
  site: 'https://mountainporchpumpkins.com',

  integrations: [
    tailwind({
      // Disable default base styles to maintain control
      applyBaseStyles: false,
    }),
    alpinejs({
      // Configure Alpine.js for optimal performance
      entrypoint: '/src/alpine.ts'
    })
  ],

  vite: {
    build: {
      // Optimize bundle splitting for mobile-first performance
      rollupOptions: {
        output: {
          manualChunks: {
            // Vendor chunks for better caching
            vendor: ['alpinejs'],
            // Separate chunk for large dependencies
            polyfills: ['core-js']
          },
          // Optimize chunk file names for caching
          chunkFileNames: 'assets/js/[name]-[hash].js',
          entryFileNames: 'assets/js/[name]-[hash].js',
          assetFileNames: (assetInfo) => {
            const info = assetInfo.name.split('.');
            const ext = info[info.length - 1];
            if (/png|jpe?g|svg|gif|tiff|bmp|ico/i.test(ext)) {
              return `assets/images/[name]-[hash][extname]`;
            }
            if (/css/i.test(ext)) {
              return `assets/css/[name]-[hash][extname]`;
            }
            return `assets/[ext]/[name]-[hash][extname]`;
          }
        }
      },
      // Optimize for production
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: true, // Remove console.log in production
          drop_debugger: true,
          pure_funcs: ['console.log', 'console.info', 'console.debug']
        },
        mangle: {
          safari10: true // Fix Safari 10 issues
        }
      },
      // Enable source maps for debugging
      sourcemap: false,
      // Optimize CSS
      cssCodeSplit: true,
      // Set chunk size warning limit
      chunkSizeWarningLimit: 1000
    },

    // Optimize for mobile development
    server: {
      host: true, // Allow external connections for mobile testing
      port: 4321
    },

    // Performance optimizations
    optimizeDeps: {
      include: ['alpinejs'],
      exclude: []
    },

    // CSS optimizations
    css: {
      devSourcemap: true,
      preprocessorOptions: {
        scss: {
          // Add any SCSS optimizations here
        }
      }
    }
  },

  // Mobile-first image optimization
  image: {
    service: {
      entrypoint: 'astro/assets/services/sharp'
    },
    domains: ['images.unsplash.com'], // Allow external image optimization
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'images.unsplash.com'
      }
    ]
  },

  // Performance optimizations
  compressHTML: true,

  build: {
    // Inline small assets for better mobile performance
    inlineStylesheets: 'auto',
    // Split CSS for better caching
    split: true,
    // Exclude unused CSS
    excludeMiddleware: true
  },

  // Security headers
  security: {
    checkOrigin: true
  },

  // Experimental features for better performance
  experimental: {
    // Remove outdated experimental flags
  },

  // Markdown optimizations
  markdown: {
    shikiConfig: {
      // Optimize syntax highlighting
      wrap: true,
      lineNumbers: false
    }
  }
});
