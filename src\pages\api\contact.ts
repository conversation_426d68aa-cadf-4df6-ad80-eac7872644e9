// Contact form API endpoint
// Handles form submissions with validation and email notifications

import type { APIRoute } from 'astro';
import type { CustomerInfo, ValidationResult } from '../../types';

// Validation functions
function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

function validatePhone(phone: string): boolean {
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
  const cleanPhone = phone.replace(/\D/g, '');
  return phoneRegex.test(cleanPhone) && cleanPhone.length >= 10;
}

function validateRequired(value: string): boolean {
  return value && value.trim().length > 0;
}

function validateFormData(data: any): ValidationResult {
  const errors: Record<string, string> = {};
  
  // Required field validation
  if (!validateRequired(data.firstName)) {
    errors.firstName = 'First name is required';
  }
  
  if (!validateRequired(data.lastName)) {
    errors.lastName = 'Last name is required';
  }
  
  if (!validateRequired(data.email)) {
    errors.email = 'Email is required';
  } else if (!validateEmail(data.email)) {
    errors.email = 'Please enter a valid email address';
  }
  
  if (!validateRequired(data.phone)) {
    errors.phone = 'Phone number is required';
  } else if (!validatePhone(data.phone)) {
    errors.phone = 'Please enter a valid phone number';
  }
  
  if (!validateRequired(data.address)) {
    errors.address = 'Address is required';
  }
  
  if (!validateRequired(data.serviceArea)) {
    errors.serviceArea = 'Service area is required';
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
}

// Format form data for email
function formatEmailContent(data: any): string {
  const packageName = data.package ? 
    data.package.replace('-', ' ').replace(/\b\w/g, (l: string) => l.toUpperCase()) : 
    'Not specified';
    
  return `
New Quote Request - Mountain Porch Pumpkins

Customer Information:
- Name: ${data.firstName} ${data.lastName}
- Email: ${data.email}
- Phone: ${data.phone}
- Address: ${data.address}
- Service Area: ${data.serviceArea}

Package Interest:
- Package: ${packageName}

Delivery Information:
- Preferred Date: ${data.deliveryDate || 'Not specified'}

Special Requests:
${data.message || 'None'}

Marketing Consent: ${data.marketingConsent ? 'Yes' : 'No'}

---
Submitted: ${new Date().toLocaleString()}
IP: ${data.ip || 'Unknown'}
User Agent: ${data.userAgent || 'Unknown'}
  `.trim();
}

// Mock email sending function (replace with actual email service)
async function sendEmail(to: string, subject: string, content: string): Promise<boolean> {
  // In production, integrate with services like:
  // - SendGrid
  // - Mailgun
  // - AWS SES
  // - Resend
  
  console.log('📧 Email would be sent:');
  console.log(`To: ${to}`);
  console.log(`Subject: ${subject}`);
  console.log(`Content:\n${content}`);
  
  // Simulate email sending delay
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // Simulate 95% success rate
  return Math.random() > 0.05;
}

// Main API handler
export const POST: APIRoute = async ({ request }) => {
  try {
    // Parse request body
    const data = await request.json();
    
    // Add request metadata
    data.ip = request.headers.get('x-forwarded-for') || 
              request.headers.get('x-real-ip') || 
              'unknown';
    data.userAgent = request.headers.get('user-agent') || 'unknown';
    
    // Validate form data
    const validation = validateFormData(data);
    
    if (!validation.isValid) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Validation failed',
        errors: validation.errors
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }
    
    // Format email content
    const emailContent = formatEmailContent(data);
    
    // Send notification email to business
    const businessEmailSent = await sendEmail(
      '<EMAIL>',
      `New Quote Request from ${data.firstName} ${data.lastName}`,
      emailContent
    );
    
    // Send confirmation email to customer
    const customerEmailSent = await sendEmail(
      data.email,
      'Thank you for your quote request - Mountain Porch Pumpkins',
      `
Hi ${data.firstName},

Thank you for your interest in Mountain Porch Pumpkins! We've received your quote request and will contact you within 24 hours with a personalized quote.

Your Request Details:
- Package: ${data.package || 'Custom consultation'}
- Service Area: ${data.serviceArea}
- Preferred Date: ${data.deliveryDate || 'To be discussed'}

In the meantime, feel free to browse our gallery at mountainporchpumpkins.com for inspiration!

If you have any immediate questions, don't hesitate to call us at (801) 555-0123.

Best regards,
The Mountain Porch Pumpkins Team

---
This is an automated confirmation. Please do not reply to this email.
      `.trim()
    );
    
    if (!businessEmailSent) {
      throw new Error('Failed to send business notification email');
    }
    
    // Log successful submission
    console.log('✅ Quote request submitted successfully:', {
      customer: `${data.firstName} ${data.lastName}`,
      email: data.email,
      package: data.package,
      serviceArea: data.serviceArea,
      timestamp: new Date().toISOString()
    });
    
    // Return success response
    return new Response(JSON.stringify({
      success: true,
      message: 'Quote request submitted successfully',
      data: {
        confirmationId: `MPP-${Date.now()}`,
        estimatedResponse: '24 hours'
      }
    }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
  } catch (error) {
    console.error('❌ Contact form submission error:', error);
    
    return new Response(JSON.stringify({
      success: false,
      error: 'Internal server error',
      message: 'Something went wrong. Please try again or call us directly at (801) 555-0123.'
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};

// Handle other HTTP methods
export const GET: APIRoute = async () => {
  return new Response(JSON.stringify({
    error: 'Method not allowed',
    message: 'This endpoint only accepts POST requests'
  }), {
    status: 405,
    headers: {
      'Content-Type': 'application/json'
    }
  });
};

export const PUT = GET;
export const DELETE = GET;
export const PATCH = GET;
