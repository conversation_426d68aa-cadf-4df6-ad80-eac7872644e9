// Contact form API endpoint
// Handles form submissions with validation and email notifications

import type { APIRoute } from 'astro';
import {
  validateAndSanitizeF<PERSON>,
  CONTACT_FORM_RULES
} from '../../utils/validation';
import {
  createSuccessResponse,
  createValidationErrorResponse,
  createServerErrorResponse,
  safeParseJson,
  extractClientMetadata,
  logApiRequest,
  checkRateLimit,
  createRateLimitResponse
} from '../../utils/api';

// Email notification function (simplified)
async function sendEmailNotification(formData: Record<string, string>): Promise<boolean> {
  try {
    // In a real implementation, this would integrate with an email service
    // For now, we'll just log the submission
    console.log('📧 Contact form submission:', {
      name: `${formData.firstName} ${formData.lastName}`,
      email: formData.email,
      package: formData.package,
      timestamp: new Date().toISOString()
    });

    // Simulate email sending delay
    await new Promise(resolve => setTimeout(resolve, 100));

    return true;
  } catch (error) {
    console.error('Email notification failed:', error);
    return false;
  }
}

// Format form data for email
function formatEmailContent(data: Record<string, string>): string {
  const packageName = data.package ?
    data.package.replace('-', ' ').replace(/\b\w/g, (l: string) => l.toUpperCase()) :
    'Not specified';

  return `
New Quote Request - Mountain Porch Pumpkins

Customer Information:
- Name: ${data.firstName} ${data.lastName}
- Email: ${data.email}
- Phone: ${data.phone}
- Address: ${data.address}

Package Interest:
- Package: ${packageName}

Special Requests:
${data.message || 'None'}

Marketing Consent: ${data.marketingConsent ? 'Yes' : 'No'}

---
Submitted: ${new Date().toLocaleString()}
  `.trim();
}

// Main API handler
export const POST: APIRoute = async ({ request }) => {
  try {
    // Extract client metadata for rate limiting
    const metadata = extractClientMetadata(request);

    // Check rate limit (10 requests per minute per IP)
    const rateLimitCheck = checkRateLimit(metadata.ip, 10, 60000);
    if (!rateLimitCheck.allowed) {
      return createRateLimitResponse(rateLimitCheck.resetTime);
    }

    // Parse request body safely
    const parseResult = await safeParseJson(request);
    if (!parseResult.success) {
      return createValidationErrorResponse({ json: parseResult.error });
    }

    const data = parseResult.data;

    // Log API request for debugging
    logApiRequest('POST', '/api/contact', data, metadata);

    // Validate and sanitize form data using shared utility
    const validation = validateAndSanitizeForm(data, CONTACT_FORM_RULES);

    if (!validation.isValid) {
      return createValidationErrorResponse(validation.errors);
    }

    // Use sanitized data for email
    const sanitizedData = validation.sanitizedData;

    // Send email notification
    const emailSent = await sendEmailNotification(sanitizedData);

    if (!emailSent) {
      console.warn('Email notification failed, but form submission was successful');
    }

    // Log successful submission
    console.log('✅ Contact form submitted successfully:', {
      customer: `${sanitizedData.firstName} ${sanitizedData.lastName}`,
      email: sanitizedData.email,
      package: sanitizedData.package,
      timestamp: new Date().toISOString()
    });

    // Return success response using shared utility
    return createSuccessResponse(
      { submissionId: `MPP-${Date.now()}` },
      'Thank you for your submission! We\'ll contact you within 24 hours.'
    );

  } catch (error) {
    console.error('❌ Contact form submission error:', error);

    // Return error response using shared utility
    return createServerErrorResponse(
      'Something went wrong. Please try again or call us directly at (801) 555-0123.'
    );
  }
};

// Handle other HTTP methods
export const GET: APIRoute = async () => {
  return createErrorResponse('Method not allowed', 405);
};

export const PUT = GET;
export const DELETE = GET;
export const PATCH = GET;
