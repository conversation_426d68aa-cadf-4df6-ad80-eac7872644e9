# Mountain Porch Pumpkins - Content Style Guide

## Word Usage Rules

### AVOID OVERUSE
- **"Professional"** - Use sparingly, only when it adds specific value
- **"Premium"** - Replace with specific descriptors (quality, fresh, handpicked)
- **"Complete"** - Implied in service, remove unless necessary
- **"And"** - Use commas or bullets instead of "and" chains

### PREFERRED TERMS
- **"Setup"** instead of "setup and arrangement"
- **"Styling"** instead of "professional styling"
- **"Pumpkins"** instead of "premium pumpkins" (unless premium adds value)
- **"Guarantee"** instead of "freshness guarantee" (context is clear)

### TONE GUIDELINES
- **Conversational**, not corporate
- **Benefit-focused**, not feature-heavy
- **Specific**, not generic
- **Scannable**, not verbose

## Content Structure Rules

### Package Descriptions
- **Maximum**: 6-8 words
- **Format**: Benefit statement or emotional hook
- **Examples**: 
  - "Cozy porch perfection"
  - "Most popular choice"
  - "Luxury estate displays"

### Feature Lists
- **Format**: Icon + 2-4 words
- **Focus**: What customer gets, not how we do it
- **Examples**:
  - ✅ "8-12 pumpkins" not "8-12 premium pumpkins in various sizes"
  - ✅ "2-week guarantee" not "2-week freshness guarantee"

### Content Categories
Organize all features into three categories:
1. **What You Get** - Quantity, variety, physical items
2. **How We Deliver** - Service level, timing, support
3. **Perfect For** - Use cases, target customers

## Universal Features
Move these common features to a global section:
- ✅ Delivery & setup included
- ✅ Freshness guarantee
- ✅ Complete styling
- ✅ Serving Utah, Idaho & Wyoming

## Mobile-First Rules
- **Hierarchy**: Most important info first
- **Scanning**: Use bullets, not paragraphs
- **Touch targets**: Minimum 44px
- **Progressive disclosure**: Show basics, reveal details on demand

## Benefit-Focused Language

### Transform Technical Features to Benefits
- "8-12 pumpkins" → "Perfect porch coverage"
- "Professional arrangement" → "Instagram-ready displays"
- "Maintenance visit" → "Stays beautiful all season"
- "Commercial-grade materials" → "Built for high traffic"

### Customer Outcome Focus
Instead of listing what we do, describe what customers achieve:
- ❌ "Professional delivery and setup"
- ✅ "Hassle-free fall magic"

## Content Testing Guidelines
- A/B test description lengths
- Measure scan-ability on mobile
- Track conversion impact of changes
- Validate benefit language with customers

## Implementation Checklist
- [ ] Remove redundant words
- [ ] Shorten descriptions to 6-8 words
- [ ] Add visual size indicators
- [ ] Create universal features section
- [ ] Implement benefit-focused language
- [ ] Test mobile readability
