// Centralized package data management for Mountain Porch Pumpkins
// Eliminates duplication between components and API endpoints

import type { Package } from '../types';

/**
 * Package pricing in cents (for Stripe compatibility)
 */
export const PACKAGE_PRICING = {
  'harvest-essentials': 34900, // $349.00
  'pumpkin-charm': 59900,      // $599.00
  'fall-favorites': 79900,     // $799.00
  'autumn-splendor': 99900     // $999.00
} as const;

/**
 * Package pricing in dollars (for display)
 */
export const PACKAGE_PRICING_DISPLAY = {
  'harvest-essentials': 349,
  'pumpkin-charm': 599,
  'fall-favorites': 799,
  'autumn-splendor': 999
} as const;

/**
 * Package configuration data
 */
export const PACKAGES: Package[] = [
  {
    id: 'harvest-essentials',
    name: 'Harvest Essentials',
    price: PACKAGE_PRICING_DISPLAY['harvest-essentials'],
    description: 'Perfect starter package for apartments and small porches',
    image: '/images/Package 1 displayed.jpg',
    category: 'residential',
    size: 'small',
    quantity: '8-12',
    features: [
      '8-12 premium pumpkins',
      'Seasonal gourds & squash',
      'Corn stalks & hay bales',
      'Professional arrangement',
      'Delivery & setup included',
      '4-week freshness guarantee'
    ],
    categorizedFeatures: {
      whatYouGet: [
        '8-12 premium pumpkins in various sizes',
        'Seasonal gourds and decorative squash',
        'Corn stalks and hay bales',
        'Natural autumn foliage'
      ],
      howWeDeliver: [
        'Professional design and arrangement',
        'Complete delivery and setup',
        'Weather-resistant placement',
        'Photo documentation of your display'
      ]
    }
  },
  {
    id: 'pumpkin-charm',
    name: 'Pumpkin Charm',
    price: PACKAGE_PRICING_DISPLAY['pumpkin-charm'],
    description: 'Charming display for small to medium porches and entryways',
    image: '/images/package 2 displayed.jpg',
    category: 'residential',
    size: 'medium',
    quantity: '15-18',
    features: [
      '15-18 premium pumpkins',
      'Variety of gourds & squash',
      'Corn stalks & decorative elements',
      'Mums or seasonal flowers',
      'Professional design & setup',
      '4-week freshness guarantee'
    ],
    categorizedFeatures: {
      whatYouGet: [
        '15-18 premium pumpkins in multiple varieties',
        'Assorted gourds and decorative squash',
        'Corn stalks and natural elements',
        'Seasonal mums or flowers',
        'Decorative autumn accessories'
      ],
      howWeDeliver: [
        'Custom design consultation',
        'Professional arrangement and setup',
        'Optimal placement for visual impact',
        'Maintenance tips and care guide'
      ]
    }
  },
  {
    id: 'fall-favorites',
    name: 'Fall Favorites',
    price: PACKAGE_PRICING_DISPLAY['fall-favorites'],
    description: 'Popular choice for medium to large porches and yards',
    image: '/images/package 3 displayed.jpg',
    category: 'residential',
    size: 'large',
    quantity: '20-25',
    features: [
      '20-25 premium pumpkins',
      'Extensive gourd collection',
      'Multiple corn stalk bundles',
      'Seasonal flowers & plants',
      'Decorative autumn accessories',
      'Professional design & maintenance',
      '4-week freshness guarantee'
    ],
    categorizedFeatures: {
      whatYouGet: [
        '20-25 premium pumpkins in various sizes and colors',
        'Extensive collection of gourds and squash',
        'Multiple corn stalk bundles',
        'Seasonal mums, flowers, and plants',
        'Decorative autumn accessories and props'
      ],
      howWeDeliver: [
        'Personalized design consultation',
        'Professional arrangement and styling',
        'Strategic placement for maximum impact',
        'Optional mid-season refresh service'
      ]
    }
  },
  {
    id: 'autumn-splendor',
    name: 'Autumn Splendor',
    price: PACKAGE_PRICING_DISPLAY['autumn-splendor'],
    description: 'Premium package for large porches and grand displays',
    image: '/images/Package 1 displayed.jpg',
    category: 'residential',
    size: 'large',
    quantity: '30+',
    features: [
      '30+ premium pumpkins',
      'Luxury gourd & squash selection',
      'Multiple corn stalk arrangements',
      'Premium seasonal flowers',
      'Custom decorative elements',
      'Professional design & styling',
      'Mid-season refresh included',
      '4-week freshness guarantee'
    ],
    categorizedFeatures: {
      whatYouGet: [
        '30+ premium pumpkins in diverse varieties',
        'Luxury selection of gourds and squash',
        'Multiple corn stalk arrangements',
        'Premium seasonal flowers and plants',
        'Custom decorative elements and props',
        'Specialty autumn accessories'
      ],
      howWeDeliver: [
        'Comprehensive design consultation',
        'Professional styling and arrangement',
        'Premium placement and presentation',
        'Mid-season refresh and maintenance',
        'Seasonal transition planning'
      ]
    }
  },
  {
    id: 'business-solutions',
    name: 'Business Solutions',
    price: 0, // Custom pricing
    description: 'Custom displays for businesses, restaurants, and commercial properties',
    image: '/images/Business/business-display-1.jpg',
    category: 'business',
    size: 'custom',
    features: [
      'Custom design consultation',
      'Scalable display options',
      'Brand-appropriate styling',
      'Multiple location service',
      'Seasonal maintenance included',
      'Professional installation',
      'Flexible scheduling'
    ],
    categorizedFeatures: {
      whatYouGet: [
        'Custom-designed displays',
        'Brand-appropriate autumn styling',
        'Scalable options for any space',
        'Professional-grade materials',
        'Weather-resistant installations'
      ],
      howWeDeliver: [
        'Business consultation and planning',
        'Professional design and installation',
        'Multiple location coordination',
        'Ongoing maintenance and support',
        'Seasonal transition services'
      ]
    }
  },
  {
    id: 'event-decor',
    name: 'Event Decor',
    price: 0, // Custom pricing
    description: 'Stunning autumn displays for weddings, parties, and special events',
    image: '/images/Business/event-display-1.jpg',
    category: 'event',
    size: 'custom',
    features: [
      'Event-specific design',
      'Coordinated color schemes',
      'Multiple display areas',
      'Setup & breakdown included',
      'Photography-ready styling',
      'Flexible timing options',
      'Custom decorative elements'
    ],
    categorizedFeatures: {
      whatYouGet: [
        'Event-specific autumn displays',
        'Coordinated color and style themes',
        'Multiple display configurations',
        'Photography-ready presentations',
        'Custom decorative elements'
      ],
      howWeDeliver: [
        'Event planning consultation',
        'Coordinated setup and styling',
        'Professional installation team',
        'Event-day support and adjustments',
        'Complete breakdown and cleanup'
      ]
    }
  }
];

/**
 * Get package by ID
 */
export function getPackageById(id: string): Package | null {
  return PACKAGES.find(pkg => pkg.id === id) || null;
}

/**
 * Get packages by category
 */
export function getPackagesByCategory(category: Package['category']): Package[] {
  return PACKAGES.filter(pkg => pkg.category === category);
}

/**
 * Get residential packages only
 */
export function getResidentialPackages(): Package[] {
  return getPackagesByCategory('residential');
}

/**
 * Get package pricing in cents (for Stripe)
 */
export function getPackagePricingCents(packageId: string): number | null {
  return PACKAGE_PRICING[packageId as keyof typeof PACKAGE_PRICING] || null;
}

/**
 * Get package pricing in dollars (for display)
 */
export function getPackagePricingDisplay(packageId: string): number | null {
  return PACKAGE_PRICING_DISPLAY[packageId as keyof typeof PACKAGE_PRICING_DISPLAY] || null;
}

/**
 * Check if package has custom pricing
 */
export function hasCustomPricing(packageId: string): boolean {
  const pkg = getPackageById(packageId);
  return pkg ? pkg.price === 0 : false;
}

/**
 * Get package options for form selects
 */
export function getPackageOptions(): Array<{ value: string; label: string; price?: number }> {
  return PACKAGES.map(pkg => ({
    value: pkg.id,
    label: pkg.price > 0 ? `${pkg.name} - $${pkg.price}` : `${pkg.name} - Custom Quote`,
    price: pkg.price > 0 ? pkg.price : undefined
  }));
}

/**
 * Format price for display
 */
export function formatPrice(price: number): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
  }).format(price);
}

/**
 * Validate package selection
 */
export function validatePackageSelection(packageId: string): { isValid: boolean; error?: string } {
  const pkg = getPackageById(packageId);
  
  if (!pkg) {
    return { isValid: false, error: 'Invalid package selection' };
  }
  
  return { isValid: true };
}
