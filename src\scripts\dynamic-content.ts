// Dynamic Content System
// Adapts content based on user context (location, device, behavior)

interface UserContext {
  location?: {
    state?: string;
    city?: string;
    zipCode?: string;
    serviceArea?: boolean;
  };
  device: {
    type: 'mobile' | 'tablet' | 'desktop';
    screenWidth: number;
    screenHeight: number;
    touchEnabled: boolean;
  };
  behavior: {
    visitCount: number;
    timeOnSite: number;
    pagesViewed: string[];
    packageInteractions: string[];
    formStarted: boolean;
    formCompleted: boolean;
  };
  preferences: {
    packageSize?: string;
    priceRange?: string;
    previousSelections: string[];
  };
}

interface ContentVariation {
  selector: string;
  variations: {
    condition: (context: UserContext) => boolean;
    content: string;
    priority: number;
  }[];
}

class DynamicContentSystem {
  private context: UserContext;
  private contentVariations: ContentVariation[] = [];

  constructor() {
    this.context = this.initializeContext();
    this.setupContentVariations();
    this.applyDynamicContent();
  }

  private initializeContext(): UserContext {
    return {
      device: this.detectDevice(),
      behavior: this.loadBehaviorData(),
      preferences: this.loadPreferences()
    };
  }

  private detectDevice() {
    const width = window.innerWidth;
    const height = window.innerHeight;
    
    return {
      type: width < 768 ? 'mobile' : width < 1024 ? 'tablet' : 'desktop',
      screenWidth: width,
      screenHeight: height,
      touchEnabled: 'ontouchstart' in window
    };
  }

  private loadBehaviorData() {
    const stored = localStorage.getItem('mpp_behavior');
    const defaultBehavior = {
      visitCount: 1,
      timeOnSite: 0,
      pagesViewed: [window.location.pathname],
      packageInteractions: [],
      formStarted: false,
      formCompleted: false
    };

    if (!stored) return defaultBehavior;

    try {
      const behavior = JSON.parse(stored);
      behavior.visitCount += 1;
      behavior.pagesViewed.push(window.location.pathname);
      return behavior;
    } catch {
      return defaultBehavior;
    }
  }

  private loadPreferences() {
    const stored = localStorage.getItem('mpp_preferences');
    const defaultPrefs = {
      previousSelections: []
    };

    if (!stored) return defaultPrefs;

    try {
      return JSON.parse(stored);
    } catch {
      return defaultPrefs;
    }
  }

  private setupContentVariations() {
    this.contentVariations = [
      // Hero section variations
      {
        selector: '.hero-title',
        variations: [
          {
            condition: (ctx) => ctx.device.type === 'mobile',
            content: 'Fall Magic Delivered',
            priority: 1
          },
          {
            condition: (ctx) => ctx.behavior.visitCount > 1,
            content: 'Welcome Back! Ready for Fall?',
            priority: 2
          }
        ]
      },

      // Package recommendations
      {
        selector: '[data-package-id="harvest-essentials"] .package-description',
        variations: [
          {
            condition: (ctx) => ctx.device.type === 'mobile',
            content: 'Perfect fit',
            priority: 1
          },
          {
            condition: (ctx) => ctx.location?.serviceArea === false,
            content: 'Coming soon to your area',
            priority: 3
          }
        ]
      },

      // Call-to-action variations
      {
        selector: '.cta-primary',
        variations: [
          {
            condition: (ctx) => ctx.behavior.formStarted && !ctx.behavior.formCompleted,
            content: 'Complete Your Order',
            priority: 3
          },
          {
            condition: (ctx) => ctx.behavior.visitCount > 2,
            content: 'Ready to Order?',
            priority: 2
          },
          {
            condition: (ctx) => ctx.device.type === 'mobile',
            content: 'Get Quote',
            priority: 1
          }
        ]
      },

      // Service area messaging
      {
        selector: '.service-area-message',
        variations: [
          {
            condition: (ctx) => ctx.location?.state === 'Utah',
            content: 'Serving your Utah community with pride!',
            priority: 2
          },
          {
            condition: (ctx) => ctx.location?.state === 'Idaho',
            content: 'Bringing fall magic to Southern Idaho!',
            priority: 2
          },
          {
            condition: (ctx) => ctx.location?.state === 'Wyoming',
            content: 'Serving Jackson Hole and Western Wyoming!',
            priority: 2
          }
        ]
      },

      // Package popularity indicators
      {
        selector: '[data-package-id="fall-favorites"] .popularity-badge',
        variations: [
          {
            condition: (ctx) => ctx.location?.city?.toLowerCase().includes('salt lake'),
            content: 'Most popular in Salt Lake City',
            priority: 2
          },
          {
            condition: (ctx) => ctx.device.type === 'mobile',
            content: 'Top choice',
            priority: 1
          }
        ]
      }
    ];
  }

  private applyDynamicContent() {
    this.contentVariations.forEach(variation => {
      const elements = document.querySelectorAll(variation.selector);
      if (elements.length === 0) return;

      // Find the highest priority variation that matches
      const applicableVariations = variation.variations
        .filter(v => v.condition(this.context))
        .sort((a, b) => b.priority - a.priority);

      if (applicableVariations.length > 0) {
        const selectedVariation = applicableVariations[0];
        elements.forEach(element => {
          element.textContent = selectedVariation.content;
        });

        console.log(`🎯 Applied dynamic content: ${variation.selector} -> ${selectedVariation.content}`);
      }
    });
  }

  public updateLocation(location: Partial<UserContext['location']>) {
    this.context.location = { ...this.context.location, ...location };
    this.applyDynamicContent();
    
    // Track location-based content
    if ((window as any).mppAnalytics) {
      (window as any).mppAnalytics.trackContentOptimization('location_detected', {
        location: this.context.location
      });
    }
  }

  public trackPackageInteraction(packageId: string) {
    this.context.behavior.packageInteractions.push(packageId);
    this.saveBehaviorData();

    // Update preferences based on interactions
    if (!this.context.preferences.previousSelections.includes(packageId)) {
      this.context.preferences.previousSelections.push(packageId);
      this.savePreferences();
    }

    // Apply content updates based on new interaction
    this.applyDynamicContent();
  }

  public trackFormProgress(started: boolean, completed: boolean = false) {
    this.context.behavior.formStarted = started;
    this.context.behavior.formCompleted = completed;
    this.saveBehaviorData();
    this.applyDynamicContent();
  }

  private saveBehaviorData() {
    localStorage.setItem('mpp_behavior', JSON.stringify(this.context.behavior));
  }

  private savePreferences() {
    localStorage.setItem('mpp_preferences', JSON.stringify(this.context.preferences));
  }

  public getContext(): UserContext {
    return { ...this.context };
  }

  // Detect user's location for service area messaging
  public async detectLocation() {
    try {
      // Try to get location from IP (using a free service)
      const response = await fetch('https://ipapi.co/json/');
      const data = await response.json();
      
      if (data.region && data.city) {
        this.updateLocation({
          state: data.region,
          city: data.city,
          zipCode: data.postal,
          serviceArea: this.isInServiceArea(data.region)
        });
      }
    } catch (error) {
      console.log('Could not detect location:', error);
    }
  }

  private isInServiceArea(state: string): boolean {
    const serviceStates = ['Utah', 'Idaho', 'Wyoming'];
    return serviceStates.includes(state);
  }

  // Personalize package recommendations
  public getPersonalizedRecommendation(): string | null {
    const { device, behavior, preferences } = this.context;

    // For mobile users, recommend smaller packages
    if (device.type === 'mobile' && device.screenWidth < 400) {
      return 'harvest-essentials';
    }

    // For returning visitors, suggest based on previous interactions
    if (behavior.visitCount > 1 && preferences.previousSelections.length > 0) {
      const lastSelection = preferences.previousSelections[preferences.previousSelections.length - 1];
      return lastSelection;
    }

    // For users who started but didn't complete form, suggest most popular
    if (behavior.formStarted && !behavior.formCompleted) {
      return 'fall-favorites';
    }

    return null;
  }
}

// Initialize dynamic content system
const dynamicContent = new DynamicContentSystem();

// Detect location on load
dynamicContent.detectLocation();

// Export for global use
(window as any).mppDynamicContent = dynamicContent;

export default dynamicContent;
