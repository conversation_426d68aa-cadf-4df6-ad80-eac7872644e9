---
// Categorized Features component
// Displays features organized by value categories for better scanning

import FeatureIcon from './FeatureIcon.astro';

export interface Props {
  categorizedFeatures?: {
    whatYouGet?: string[];
    howWeDeliver?: string[];
    perfectFor?: string[];
  };
  fallbackFeatures?: string[];
  class?: string;
  variant?: 'default' | 'compact' | 'modal';
}

const { 
  categorizedFeatures,
  fallbackFeatures = [],
  class: className = '',
  variant = 'default'
} = Astro.props;

// Use categorized features if available, otherwise fall back to regular features
const hasCategories = categorizedFeatures && (
  categorizedFeatures.whatYouGet?.length || 
  categorizedFeatures.howWeDeliver?.length || 
  categorizedFeatures.perfectFor?.length
);

const getVariantClasses = () => {
  switch (variant) {
    case 'compact':
      return 'space-y-3';
    case 'modal':
      return 'space-y-4';
    default:
      return 'space-y-4';
  }
};

const getSectionClasses = () => {
  switch (variant) {
    case 'compact':
      return 'text-xs';
    case 'modal':
      return 'text-sm';
    default:
      return 'text-sm';
  }
};
---

<div class:list={[getVariantClasses(), className]}>
  {hasCategories ? (
    <!-- Categorized Features Display -->
    <div class="grid gap-4">
      {categorizedFeatures.whatYouGet && categorizedFeatures.whatYouGet.length > 0 && (
        <div>
          <h4 class="font-semibold text-gray-900 mb-2 flex items-center">
            <span class="text-lg mr-2">🎃</span>
            What You Get
          </h4>
          <ul class="space-y-1">
            {categorizedFeatures.whatYouGet.map(feature => (
              <li class:list={['flex items-center text-gray-600', getSectionClasses()]}>
                <FeatureIcon feature={feature} class="mr-2" variant="small" />
                {feature}
              </li>
            ))}
          </ul>
        </div>
      )}

      {categorizedFeatures.howWeDeliver && categorizedFeatures.howWeDeliver.length > 0 && (
        <div>
          <h4 class="font-semibold text-gray-900 mb-2 flex items-center">
            <span class="text-lg mr-2">🚚</span>
            How We Deliver
          </h4>
          <ul class="space-y-1">
            {categorizedFeatures.howWeDeliver.map(feature => (
              <li class:list={['flex items-center text-gray-600', getSectionClasses()]}>
                <FeatureIcon feature={feature} class="mr-2" variant="small" />
                {feature}
              </li>
            ))}
          </ul>
        </div>
      )}

      {categorizedFeatures.perfectFor && categorizedFeatures.perfectFor.length > 0 && (
        <div>
          <h4 class="font-semibold text-gray-900 mb-2 flex items-center">
            <span class="text-lg mr-2">🎯</span>
            Perfect For
          </h4>
          <ul class="space-y-1">
            {categorizedFeatures.perfectFor.map(feature => (
              <li class:list={['flex items-center text-gray-600', getSectionClasses()]}>
                <FeatureIcon feature={feature} class="mr-2" variant="small" />
                {feature}
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  ) : (
    <!-- Fallback to Regular Features -->
    <div>
      <h4 class="font-semibold text-gray-900 mb-2">Features</h4>
      <ul class="space-y-1">
        {fallbackFeatures.map(feature => (
          <li class:list={['flex items-center text-gray-600', getSectionClasses()]}>
            <FeatureIcon feature={feature} class="mr-2" variant="small" />
            {feature}
          </li>
        ))}
      </ul>
    </div>
  )}
</div>

<style>
  /* Ensure consistent spacing and alignment */
  .grid {
    grid-template-columns: 1fr;
  }
  
  /* Mobile optimizations */
  @media (max-width: 640px) {
    .space-y-4 > * + * {
      margin-top: 0.75rem;
    }
    
    .space-y-3 > * + * {
      margin-top: 0.5rem;
    }
  }
  
  /* Hover effects for better interactivity */
  li:hover {
    color: rgb(55 65 81); /* text-gray-700 */
    transition: color 0.2s ease-in-out;
  }
</style>
