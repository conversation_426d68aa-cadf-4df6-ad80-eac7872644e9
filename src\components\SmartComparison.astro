---
// Smart Comparison component
// Intelligent package comparison that highlights relevant differences

import FeatureIcon from './FeatureIcon.astro';

export interface Props {
  packages: any[];
  selectedPackages?: string[];
  class?: string;
  variant?: 'modal' | 'sidebar' | 'fullscreen';
}

const { 
  packages,
  selectedPackages = [],
  class: className = '',
  variant = 'modal'
} = Astro.props;

const getVariantClasses = () => {
  switch (variant) {
    case 'sidebar':
      return 'w-80 h-full bg-white border-l border-gray-200';
    case 'fullscreen':
      return 'fixed inset-0 bg-white z-50';
    default:
      return 'bg-white rounded-xl shadow-xl max-w-4xl mx-auto';
  }
};
---

<div 
  class:list={[
    'smart-comparison',
    getVariantClasses(),
    className
  ]}
  data-component="smart-comparison"
  style="display: none;"
>
  <!-- Header -->
  <div class="border-b border-gray-200 p-6">
    <div class="flex items-center justify-between">
      <div>
        <h2 class="text-2xl font-bold text-gray-900">Smart Package Comparison</h2>
        <p class="text-gray-600 mt-1">Compare packages side-by-side with intelligent insights</p>
      </div>
      <button 
        class="close-comparison p-2 text-gray-400 hover:text-gray-600 rounded-lg"
        onclick="document.querySelector('.smart-comparison').style.display='none'"
      >
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
    </div>
  </div>

  <!-- Package Selection -->
  <div class="p-6 border-b border-gray-200">
    <h3 class="text-lg font-semibold text-gray-900 mb-3">Select Packages to Compare</h3>
    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3">
      {packages.map(pkg => (
        <button 
          class="package-selector p-3 border border-gray-200 rounded-lg text-center hover:border-primary-300 transition-colors"
          data-package-id={pkg.id}
          onclick="togglePackageSelection(this)"
        >
          <div class="text-2xl mb-1">🎃</div>
          <div class="text-xs font-medium text-gray-900">{pkg.name}</div>
          <div class="text-xs text-gray-600">${pkg.price}</div>
        </button>
      ))}
    </div>
  </div>

  <!-- Comparison Table -->
  <div class="comparison-content p-6">
    <div class="comparison-table-container">
      <!-- Dynamic comparison table will be inserted here -->
    </div>
  </div>

  <!-- Smart Insights -->
  <div class="smart-insights p-6 bg-gray-50 border-t border-gray-200">
    <h3 class="text-lg font-semibold text-gray-900 mb-3">Smart Insights</h3>
    <div class="insights-content space-y-3">
      <!-- Dynamic insights will be inserted here -->
    </div>
  </div>
</div>

<script>
  let selectedPackages = [];
  let allPackages = [];

  // Initialize comparison tool
  document.addEventListener('DOMContentLoaded', () => {
    // Load package data (this would normally come from props)
    allPackages = [
      {
        id: 'harvest-essentials',
        name: 'Harvest Essentials',
        price: 349,
        description: 'Cozy porch perfection',
        quantity: '8-12',
        size: 'small',
        features: ['Perfect porch coverage', 'Beautiful seasonal variety'],
        coverage: '4-10 sq ft',
        guarantee: '2 weeks',
        maintenance: false
      },
      {
        id: 'pumpkin-charm',
        name: 'Pumpkin Charm',
        price: 599,
        description: 'Beautiful color combinations',
        quantity: '15-18',
        size: 'medium',
        features: ['Generous pumpkin variety', 'Stunning color palette', 'Multiple size options'],
        coverage: '10-20 sq ft',
        guarantee: '3 weeks',
        maintenance: 'optional'
      },
      {
        id: 'fall-favorites',
        name: 'Fall Favorites',
        price: 799,
        description: 'Most popular choice',
        quantity: '15-18',
        size: 'large',
        features: ['Instagram-ready displays', 'Designer styling', 'Coordinated aesthetics'],
        coverage: '15-25 sq ft',
        guarantee: '3 weeks',
        maintenance: true
      },
      {
        id: 'autumn-splendor',
        name: 'Autumn Splendor',
        price: 999,
        description: 'Luxury estate displays',
        quantity: '20+',
        size: 'large',
        features: ['Spectacular abundance', 'Luxury-grade materials', 'Grand estate presence'],
        coverage: '25+ sq ft',
        guarantee: '4 weeks',
        maintenance: true
      }
    ];
  });

  function togglePackageSelection(button) {
    const packageId = button.dataset.packageId;
    const isSelected = selectedPackages.includes(packageId);

    if (isSelected) {
      selectedPackages = selectedPackages.filter(id => id !== packageId);
      button.classList.remove('border-primary-500', 'bg-primary-50');
      button.classList.add('border-gray-200');
    } else {
      if (selectedPackages.length < 3) { // Limit to 3 packages
        selectedPackages.push(packageId);
        button.classList.remove('border-gray-200');
        button.classList.add('border-primary-500', 'bg-primary-50');
      }
    }

    updateComparisonTable();
    updateSmartInsights();
  }

  function updateComparisonTable() {
    const container = document.querySelector('.comparison-table-container');
    if (!container || selectedPackages.length < 2) {
      container.innerHTML = '<p class="text-gray-500 text-center py-8">Select at least 2 packages to compare</p>';
      return;
    }

    const selectedData = selectedPackages.map(id => 
      allPackages.find(pkg => pkg.id === id)
    ).filter(Boolean);

    const comparisonRows = [
      { label: 'Price', key: 'price', format: (val) => `$${val}` },
      { label: 'Quantity', key: 'quantity' },
      { label: 'Coverage', key: 'coverage' },
      { label: 'Guarantee', key: 'guarantee' },
      { label: 'Maintenance', key: 'maintenance', format: (val) => val === true ? 'Included' : val === 'optional' ? 'Optional' : 'Not included' }
    ];

    let tableHTML = `
      <div class="overflow-x-auto">
        <table class="w-full">
          <thead>
            <tr class="border-b border-gray-200">
              <th class="text-left py-3 px-4 font-semibold text-gray-900">Feature</th>
              ${selectedData.map(pkg => `
                <th class="text-center py-3 px-4 font-semibold text-gray-900">${pkg.name}</th>
              `).join('')}
            </tr>
          </thead>
          <tbody>
            ${comparisonRows.map(row => `
              <tr class="border-b border-gray-100">
                <td class="py-3 px-4 font-medium text-gray-700">${row.label}</td>
                ${selectedData.map(pkg => {
                  const value = pkg[row.key];
                  const formattedValue = row.format ? row.format(value) : value;
                  const isHighlight = shouldHighlight(row.key, value, selectedData);
                  return `
                    <td class="py-3 px-4 text-center ${isHighlight ? 'bg-green-50 text-green-800 font-semibold' : 'text-gray-600'}">
                      ${formattedValue}
                    </td>
                  `;
                }).join('')}
              </tr>
            `).join('')}
          </tbody>
        </table>
      </div>
    `;

    container.innerHTML = tableHTML;
  }

  function shouldHighlight(key, value, allData) {
    switch (key) {
      case 'price':
        return value === Math.min(...allData.map(d => d.price));
      case 'quantity':
        return value.includes('20+');
      case 'guarantee':
        return value.includes('4 weeks');
      case 'maintenance':
        return value === true;
      default:
        return false;
    }
  }

  function updateSmartInsights() {
    const container = document.querySelector('.insights-content');
    if (!container || selectedPackages.length < 2) {
      container.innerHTML = '';
      return;
    }

    const selectedData = selectedPackages.map(id => 
      allPackages.find(pkg => pkg.id === id)
    ).filter(Boolean);

    const insights = generateSmartInsights(selectedData);
    
    container.innerHTML = insights.map(insight => `
      <div class="flex items-start gap-3 p-3 bg-white rounded-lg border border-gray-200">
        <div class="text-2xl">${insight.icon}</div>
        <div>
          <h4 class="font-medium text-gray-900">${insight.title}</h4>
          <p class="text-sm text-gray-600">${insight.description}</p>
        </div>
      </div>
    `).join('');
  }

  function generateSmartInsights(packages) {
    const insights = [];

    // Price comparison insight
    const prices = packages.map(p => p.price);
    const cheapest = packages.find(p => p.price === Math.min(...prices));
    const mostExpensive = packages.find(p => p.price === Math.max(...prices));
    
    if (cheapest && mostExpensive && cheapest.id !== mostExpensive.id) {
      const savings = mostExpensive.price - cheapest.price;
      insights.push({
        icon: '💰',
        title: 'Best Value',
        description: `${cheapest.name} saves you $${savings} compared to ${mostExpensive.name}`
      });
    }

    // Size recommendation
    const hasSmall = packages.some(p => p.size === 'small');
    const hasLarge = packages.some(p => p.size === 'large');
    
    if (hasSmall && hasLarge) {
      insights.push({
        icon: '📏',
        title: 'Size Consideration',
        description: 'Consider your space size - larger packages may overwhelm smaller porches'
      });
    }

    // Maintenance insight
    const withMaintenance = packages.filter(p => p.maintenance === true);
    if (withMaintenance.length > 0) {
      insights.push({
        icon: '🔧',
        title: 'Maintenance Included',
        description: `${withMaintenance.map(p => p.name).join(' and ')} include maintenance for lasting beauty`
      });
    }

    // Popularity insight
    const hasFallFavorites = packages.some(p => p.id === 'fall-favorites');
    if (hasFallFavorites) {
      insights.push({
        icon: '⭐',
        title: 'Popular Choice',
        description: 'Fall Favorites is our most popular package - great for first-time customers'
      });
    }

    return insights;
  }

  // Global function to open comparison
  window.openSmartComparison = function(packageIds = []) {
    const comparison = document.querySelector('.smart-comparison');
    comparison.style.display = 'block';
    
    // Pre-select packages if provided
    if (packageIds.length > 0) {
      selectedPackages = packageIds;
      packageIds.forEach(id => {
        const button = document.querySelector(`[data-package-id="${id}"]`);
        if (button) {
          button.classList.add('border-primary-500', 'bg-primary-50');
        }
      });
      updateComparisonTable();
      updateSmartInsights();
    }

    // Track analytics
    if (window.mppAnalytics) {
      window.mppAnalytics.track({
        event: 'smart_comparison_opened',
        category: 'tools',
        action: 'comparison_opened',
        customData: { preSelectedPackages: packageIds }
      });
    }
  };
</script>

<style>
  .package-selector.selected {
    border-color: rgb(59 130 246);
    background-color: rgb(239 246 255);
  }

  .comparison-table-container table {
    border-collapse: collapse;
  }

  .smart-comparison {
    max-height: 90vh;
    overflow-y: auto;
  }

  /* Mobile optimizations */
  @media (max-width: 768px) {
    .smart-comparison {
      position: fixed;
      inset: 0;
      border-radius: 0;
      max-height: 100vh;
    }
    
    .grid.grid-cols-2.md\\:grid-cols-3.lg\\:grid-cols-6 {
      grid-template-columns: repeat(2, 1fr);
    }
  }
</style>
