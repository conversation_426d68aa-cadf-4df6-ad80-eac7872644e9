---
// Photo Carousel component following SOLID principles
// Single Responsibility: Display photo slideshow with mobile-first touch support
// Open/Closed: Extensible through props and photo data
// Interface Segregation: Clean photo interface

import type { ShowcasePhoto } from '../types';
import OptimizedImage from './OptimizedImage.astro';

export interface Props {
  photos: ShowcasePhoto[];
  autoPlay?: boolean;
  interval?: number;
  showThumbnails?: boolean;
  class?: string;
  id?: string;
}

const {
  photos,
  autoPlay = true,
  interval = 4000,
  showThumbnails = true,
  class: className = '',
  id = `carousel-${Math.random().toString(36).substr(2, 9)}`
} = Astro.props;

// Ensure we have photos
if (!photos || photos.length === 0) {
  throw new Error('PhotoCarousel requires at least one photo');
}
---

<div
  class:list={[
    'relative bg-gray-900 rounded-2xl overflow-hidden shadow-2xl lazy-component',
    className
  ]}
  data-component="carousel"
  x-data={`carouselData_${id.replace(/[^a-zA-Z0-9]/g, '_')}`}
  x-init="init()"
>
  <!-- Main Carousel Container -->
  <div class="carousel-container relative aspect-video overflow-hidden">
    <!-- Carousel Slides -->
    <div class="relative w-full h-full">
      {photos.map((photo, index) => (
        <div
          class="carousel-slide absolute inset-0 transition-opacity duration-700 ease-in-out"
          x-show={`currentIndex === ${index}`}
          x-transition:enter="transition ease-in-out duration-700"
          x-transition:enter-start="opacity-0"
          x-transition:enter-end="opacity-100"
          x-transition:leave="transition ease-in-out duration-700"
          x-transition:leave-start="opacity-100"
          x-transition:leave-end="opacity-0"
          :style={`'z-index: ' + (currentIndex === ${index} ? 20 : 10) + '; opacity: ' + (currentIndex === ${index} ? 1 : 0) + ';'`}
        >
          <!-- Photo -->
          <OptimizedImage
            src={photo.image}
            alt={photo.alt}
            width={1200}
            height={675}
            sizes="100vw"
            class="w-full h-full object-cover"
            loading={index === 0 ? 'eager' : 'lazy'}
            placeholder="blur"
            priority={index === 0}
          />
          
          <!-- Clean image display without text overlays -->
        </div>
      ))}
    </div>
    
    <!-- Touch Area for Mobile Gestures and Click to Pause -->
    <div
      class="absolute inset-0 z-10 cursor-pointer"
      @touchstart="handleTouchStart($event)"
      @touchend="handleTouchEnd($event)"
      @click="toggleAutoPlay()"
      @mouseenter="pauseAutoPlay()"
      @mouseleave="resumeAutoPlay()"
    ></div>
    
    <!-- Navigation Arrows (Desktop) -->
    <div class="hidden md:block">
      <!-- Previous Button -->
      <button 
        @click="prevSlide()"
        class="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-3 rounded-full transition-all hover:scale-110 tap-target z-20"
        aria-label="Previous photo"
      >
        <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
        </svg>
      </button>
      
      <!-- Next Button -->
      <button 
        @click="nextSlide()"
        class="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-3 rounded-full transition-all hover:scale-110 tap-target z-20"
        aria-label="Next photo"
      >
        <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
        </svg>
      </button>
    </div>
    
    <!-- Pause button removed - click on image to pause/resume -->
    
    <!-- Progress indicators removed for cleaner look -->
  </div>
  
  <!-- Thumbnail Grid (Optional) -->
  {showThumbnails && photos.length > 1 && (
    <div class="mt-4 grid grid-cols-4 sm:grid-cols-6 md:grid-cols-8 gap-2">
      {photos.map((photo, index) => (
        <button 
          @click={`goToSlide(${index})`}
          class="relative aspect-square rounded-lg overflow-hidden transition-all hover:scale-105"
          :class={`currentIndex === ${index} ? 'ring-2 ring-primary-500 ring-offset-2' : 'opacity-70 hover:opacity-100'`}
        >
          <OptimizedImage
            src={photo.image}
            alt={photo.alt}
            width={200}
            height={200}
            sizes="(max-width: 640px) 25vw, (max-width: 768px) 16vw, 12vw"
            class="w-full h-full object-cover"
            loading="lazy"
            placeholder="blur"
          />
          <div 
            class="absolute inset-0 bg-black/20 transition-opacity"
            :class={`currentIndex === ${index} ? 'opacity-0' : 'opacity-100'`}
          ></div>
        </button>
      ))}
    </div>
  )}
  
  <!-- Mobile swipe indicator removed for cleaner design -->
</div>

<style>
  /* Enhanced crossfade transitions */
  .carousel-slide {
    transition: opacity 0.7s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: opacity;
  }

  /* Smooth crossfade effect */
  .carousel-slide.entering {
    opacity: 0;
    animation: fadeIn 0.7s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  }

  .carousel-slide.leaving {
    opacity: 1;
    animation: fadeOut 0.7s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  }

  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  @keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
  }

  /* Prevent layout shift during transitions */
  .carousel-container {
    position: relative;
    overflow: hidden;
  }

  .carousel-slide img {
    transition: none; /* Prevent image-specific transitions that might conflict */
  }

  /* Optimize for performance */
  .carousel-slide {
    transform: translateZ(0); /* Force hardware acceleration */
    backface-visibility: hidden;
  }
</style>

<script is:inline set:html={`
  // Create unique carousel data function
  window.carouselData_${id.replace(/[^a-zA-Z0-9]/g, '_')} = {
    photos: ${JSON.stringify(photos)},
    currentIndex: 0,
    isPlaying: ${autoPlay},
    isPaused: false,
    interval: null,
    touchStartX: null,
    autoPlayInterval: ${interval},
    transitionDuration: 700,

    init() {
      if (this.isPlaying) {
        this.startAutoPlay();
      }

      // Handle visibility change for performance
      document.addEventListener('visibilitychange', () => {
        if (document.hidden) {
          this.pauseAutoPlay();
        } else if (this.isPlaying && !this.isPaused) {
          this.startAutoPlay();
        }
      });
    },

    goToSlide(index) {
      this.currentIndex = index;
      this.resetAutoPlay();
    },

    nextSlide() {
      this.currentIndex = (this.currentIndex + 1) % this.photos.length;
      this.resetAutoPlay();
    },

    prevSlide() {
      this.currentIndex = this.currentIndex === 0
        ? this.photos.length - 1
        : this.currentIndex - 1;
      this.resetAutoPlay();
    },

    startAutoPlay() {
      if (this.interval) clearInterval(this.interval);
      this.interval = setInterval(() => {
        if (!this.isPaused) {
          this.nextSlide();
        }
      }, this.autoPlayInterval);
    },

    pauseAutoPlay() {
      this.isPaused = true;
    },

    resumeAutoPlay() {
      this.isPaused = false;
    },

    toggleAutoPlay() {
      this.isPlaying = !this.isPlaying;
      if (this.isPlaying) {
        this.startAutoPlay();
      } else {
        clearInterval(this.interval);
      }
    },

    resetAutoPlay() {
      if (this.isPlaying) {
        this.startAutoPlay();
      }
    },

    // Mobile touch handling
    handleTouchStart(event) {
      this.touchStartX = event.touches[0].clientX;
      this.pauseAutoPlay();
    },

    handleTouchEnd(event) {
      if (!this.touchStartX) return;

      const touchEndX = event.changedTouches[0].clientX;
      const diff = this.touchStartX - touchEndX;

      // Minimum swipe distance (50px)
      if (Math.abs(diff) > 50) {
        if (diff > 0) {
          this.nextSlide();
        } else {
          this.prevSlide();
        }
      }

      this.resumeAutoPlay();
      this.touchStartX = null;
    }
  };
`}></script>


