// Shared form component utilities
// Eliminates duplicate form field patterns across components

import { FORM_FIELD_CONFIGS } from './validation';
import { getFormFieldClasses } from './component-styles';

export interface FormFieldProps {
  id: string;
  name: string;
  label: string;
  value?: string;
  required?: boolean;
  placeholder?: string;
  type?: string;
  autocomplete?: string;
  maxlength?: number;
  rows?: number;
  options?: Array<{ value: string; label: string }>;
  validation?: string[];
  className?: string;
  variant?: 'default' | 'inline' | 'compact';
}

/**
 * Generate form field HTML attributes
 */
export function getFormFieldAttributes(fieldName: string, overrides: Partial<FormFieldProps> = {}): FormFieldProps {
  const config = FORM_FIELD_CONFIGS[fieldName as keyof typeof FORM_FIELD_CONFIGS];
  
  if (!config) {
    throw new Error(`Unknown form field: ${fieldName}`);
  }

  return {
    id: fieldName,
    name: fieldName,
    label: formatFieldLabel(fieldName),
    type: config.type,
    autocomplete: config.autocomplete,
    placeholder: config.placeholder,
    required: config.required,
    maxlength: config.maxlength,
    rows: config.rows,
    validation: config.validation,
    variant: 'default',
    ...overrides
  };
}

/**
 * Format field name to human-readable label
 */
export function formatFieldLabel(fieldName: string): string {
  const labels: Record<string, string> = {
    firstName: 'First Name',
    lastName: 'Last Name',
    email: 'Email Address',
    phone: 'Phone Number',
    streetAddress: 'Street Address',
    city: 'City',
    state: 'State',
    zipCode: 'ZIP Code',
    message: 'Special Requests or Questions',
    package: 'Interested Package'
  };

  return labels[fieldName] || fieldName.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
}

/**
 * Generate Alpine.js validation attributes
 */
export function getValidationAttributes(fieldName: string, validation: string[] = []): string {
  const validationString = validation.length > 0 
    ? `['${validation.join("', '")}']` 
    : "[]";
  
  return `@blur="validateField('${fieldName}', $event.target.value, ${validationString})"`;
}

/**
 * Generate Alpine.js model attribute
 */
export function getModelAttribute(fieldName: string): string {
  return `x-model="formData.${fieldName}"`;
}

/**
 * Generate error display attributes
 */
export function getErrorAttributes(fieldName: string): {
  show: string;
  text: string;
  class: string;
} {
  const { error } = getFormFieldClasses();
  
  return {
    show: `x-show="errors.${fieldName}"`,
    text: `x-text="errors.${fieldName}"`,
    class: `${error} style="display: none;"`
  };
}

/**
 * Generate complete form field data for templates
 */
export function generateFormFieldData(fieldName: string, overrides: Partial<FormFieldProps> = {}) {
  const props = getFormFieldAttributes(fieldName, overrides);
  const classes = getFormFieldClasses(props.variant);
  const validation = getValidationAttributes(fieldName, props.validation);
  const model = getModelAttribute(fieldName);
  const error = getErrorAttributes(fieldName);

  return {
    props,
    classes,
    validation,
    model,
    error,
    // Helper methods for templates
    getLabelHtml: () => `<label for="${props.id}" class="${classes.label}">${props.label}${props.required ? ' *' : ''}</label>`,
    getInputHtml: () => {
      const baseAttrs = `
        id="${props.id}"
        name="${props.name}"
        ${model}
        ${validation}
        class="${classes.input}"
        ${props.placeholder ? `placeholder="${props.placeholder}"` : ''}
        ${props.required ? 'required' : ''}
        ${props.maxlength ? `maxlength="${props.maxlength}"` : ''}
        ${props.autocomplete ? `autocomplete="${props.autocomplete}"` : ''}
      `.trim();

      if (props.type === 'textarea') {
        return `<textarea ${baseAttrs} ${props.rows ? `rows="${props.rows}"` : ''}></textarea>`;
      } else if (props.type === 'select' && props.options) {
        const optionsHtml = props.options.map(opt => 
          `<option value="${opt.value}">${opt.label}</option>`
        ).join('\n');
        return `<select ${baseAttrs}>\n${optionsHtml}\n</select>`;
      } else {
        return `<input type="${props.type}" ${baseAttrs} />`;
      }
    },
    getErrorHtml: () => `<p ${error.show} ${error.text} class="${error.class}"></p>`
  };
}

/**
 * Common form field configurations
 */
export const COMMON_FORM_FIELDS = {
  personalInfo: ['firstName', 'lastName'],
  contactInfo: ['email', 'phone'],
  addressInfo: ['streetAddress', 'city', 'state', 'zipCode'],
  packageSelection: ['package'],
  additionalInfo: ['message']
} as const;

/**
 * Generate form section data
 */
export function generateFormSection(sectionName: keyof typeof COMMON_FORM_FIELDS, overrides: Record<string, Partial<FormFieldProps>> = {}) {
  const fieldNames = COMMON_FORM_FIELDS[sectionName];
  
  return fieldNames.map(fieldName => 
    generateFormFieldData(fieldName, overrides[fieldName] || {})
  );
}

/**
 * US States options for state dropdown
 */
export const US_STATES = [
  { value: '', label: 'Select State' },
  { value: 'AL', label: 'Alabama' },
  { value: 'AK', label: 'Alaska' },
  { value: 'AZ', label: 'Arizona' },
  { value: 'AR', label: 'Arkansas' },
  { value: 'CA', label: 'California' },
  { value: 'CO', label: 'Colorado' },
  { value: 'CT', label: 'Connecticut' },
  { value: 'DE', label: 'Delaware' },
  { value: 'FL', label: 'Florida' },
  { value: 'GA', label: 'Georgia' },
  { value: 'HI', label: 'Hawaii' },
  { value: 'ID', label: 'Idaho' },
  { value: 'IL', label: 'Illinois' },
  { value: 'IN', label: 'Indiana' },
  { value: 'IA', label: 'Iowa' },
  { value: 'KS', label: 'Kansas' },
  { value: 'KY', label: 'Kentucky' },
  { value: 'LA', label: 'Louisiana' },
  { value: 'ME', label: 'Maine' },
  { value: 'MD', label: 'Maryland' },
  { value: 'MA', label: 'Massachusetts' },
  { value: 'MI', label: 'Michigan' },
  { value: 'MN', label: 'Minnesota' },
  { value: 'MS', label: 'Mississippi' },
  { value: 'MO', label: 'Missouri' },
  { value: 'MT', label: 'Montana' },
  { value: 'NE', label: 'Nebraska' },
  { value: 'NV', label: 'Nevada' },
  { value: 'NH', label: 'New Hampshire' },
  { value: 'NJ', label: 'New Jersey' },
  { value: 'NM', label: 'New Mexico' },
  { value: 'NY', label: 'New York' },
  { value: 'NC', label: 'North Carolina' },
  { value: 'ND', label: 'North Dakota' },
  { value: 'OH', label: 'Ohio' },
  { value: 'OK', label: 'Oklahoma' },
  { value: 'OR', label: 'Oregon' },
  { value: 'PA', label: 'Pennsylvania' },
  { value: 'RI', label: 'Rhode Island' },
  { value: 'SC', label: 'South Carolina' },
  { value: 'SD', label: 'South Dakota' },
  { value: 'TN', label: 'Tennessee' },
  { value: 'TX', label: 'Texas' },
  { value: 'UT', label: 'Utah' },
  { value: 'VT', label: 'Vermont' },
  { value: 'VA', label: 'Virginia' },
  { value: 'WA', label: 'Washington' },
  { value: 'WV', label: 'West Virginia' },
  { value: 'WI', label: 'Wisconsin' },
  { value: 'WY', label: 'Wyoming' }
] as const;
