// Form store - Single Responsibility Principle
// Handles contact form state and validation

export interface FormStore {
  formData: Record<string, string>;
  formErrors: Record<string, string>;
  formMode: 'order' | 'quote';
  loading: boolean;
  updateField(field: string, value: string): void;
  setError(field: string, error: string): void;
  clearError(field: string): void;
  clearAllErrors(): void;
  setFormMode(mode: 'order' | 'quote'): void;
  setLoading(loading: boolean): void;
  resetForm(): void;
}

export function createFormStore(): FormStore {
  return {
    formData: {},
    formErrors: {},
    formMode: 'order',
    loading: false,

    updateField(field: string, value: string) {
      this.formData[field] = value;
      // Clear error when user starts typing
      if (this.formErrors[field]) {
        this.clearError(field);
      }
    },

    setError(field: string, error: string) {
      this.formErrors[field] = error;
    },

    clearError(field: string) {
      delete this.formErrors[field];
    },

    clearAllErrors() {
      this.formErrors = {};
    },

    setFormMode(mode: 'order' | 'quote') {
      this.formMode = mode;
      this.clearAllErrors();
    },

    setLoading(loading: boolean) {
      this.loading = loading;
    },

    resetForm() {
      this.formData = {};
      this.formErrors = {};
      this.formMode = 'order';
      this.loading = false;
    }
  };
}
