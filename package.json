{"name": "mountain-porch-pumpkins-optimized", "type": "module", "version": "1.0.0", "description": "Performance-optimized Mountain Porch Pumpkins website built with Astro, TypeScript, Tailwind CSS, and Alpine.js", "scripts": {"dev": "astro dev --host", "build": "astro check && astro build", "preview": "astro preview --host", "astro": "astro", "check": "astro check", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.astro", "format": "prettier --write src", "test": "echo \"Tests will be added in Phase 6\" && exit 0"}, "keywords": ["pumpkin delivery", "fall decorations", "autumn displays", "Utah", "Idaho", "Wyoming", "astro", "typescript", "tailwindcss", "alpinej<PERSON>", "mobile-first"], "author": "Mountain Porch Pumpkins", "license": "MIT", "dependencies": {"@astrojs/check": "^0.9.4", "@astrojs/node": "^9.2.2", "astro": "^5.9.4", "typescript": "^5.8.3"}, "devDependencies": {"@astrojs/alpinejs": "^0.4.0", "@astrojs/tailwind": "^6.0.2", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.15", "@types/alpinejs": "^3.13.10", "alpinejs": "^3.14.7", "sharp": "^0.33.5", "tailwindcss": "^3.4.17", "terser": "^5.43.0"}}