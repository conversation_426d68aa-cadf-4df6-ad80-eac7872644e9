---
// Personalized Recommendation component
// Shows targeted package recommendations based on user context

export interface Props {
  packages: any[];
  class?: string;
  variant?: 'banner' | 'inline' | 'modal';
}

const { 
  packages,
  class: className = '',
  variant = 'banner'
} = Astro.props;

const getVariantClasses = () => {
  switch (variant) {
    case 'inline':
      return 'bg-primary-50 border border-primary-200 rounded-lg p-4';
    case 'modal':
      return 'bg-white border border-gray-200 rounded-xl p-6 shadow-lg';
    default:
      return 'bg-gradient-to-r from-primary-100 to-secondary-100 rounded-xl p-6 shadow-sm';
  }
};
---

<div 
  class:list={[
    'personalized-recommendation',
    getVariantClasses(),
    className
  ]}
  data-component="personalized-recommendation"
>
  <div class="flex items-start gap-4">
    <!-- Recommendation Icon -->
    <div class="flex-shrink-0">
      <div class="w-10 h-10 bg-primary-500 rounded-full flex items-center justify-center">
        <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
        </svg>
      </div>
    </div>

    <!-- Recommendation Content -->
    <div class="flex-1 min-w-0">
      <h3 class="text-lg font-semibold text-gray-900 mb-2">
        <span class="recommendation-title">Recommended for You</span>
      </h3>
      
      <p class="text-gray-600 mb-4">
        <span class="recommendation-message">Based on your preferences, we think you'll love this package.</span>
      </p>

      <!-- Recommended Package Preview -->
      <div class="recommended-package-preview bg-white rounded-lg p-4 border border-gray-200">
        <div class="flex items-center gap-3">
          <div class="w-12 h-12 bg-gray-200 rounded-lg flex-shrink-0">
            <span class="recommended-package-icon text-2xl">🎃</span>
          </div>
          <div class="flex-1 min-w-0">
            <h4 class="font-medium text-gray-900 recommended-package-name">
              Fall Favorites
            </h4>
            <p class="text-sm text-gray-600 recommended-package-reason">
              Most popular choice for your area
            </p>
          </div>
          <div class="text-right">
            <div class="text-lg font-bold text-primary-600 recommended-package-price">
              $799
            </div>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex gap-3 mt-4">
        <button 
          class="recommended-package-cta flex-1 bg-primary-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-primary-700 transition-colors"
          onclick="window.mppDynamicContent?.trackPackageInteraction('recommended')"
        >
          View Details
        </button>
        <button 
          class="dismiss-recommendation px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
          onclick="this.closest('.personalized-recommendation').style.display='none'"
        >
          Dismiss
        </button>
      </div>
    </div>
  </div>
</div>

<script>
  // Initialize personalized recommendations
  document.addEventListener('DOMContentLoaded', () => {
    const recommendationElement = document.querySelector('[data-component="personalized-recommendation"]');
    if (!recommendationElement) return;

    // Wait for dynamic content system to load
    const checkDynamicContent = () => {
      if (window.mppDynamicContent) {
        updateRecommendation();
      } else {
        setTimeout(checkDynamicContent, 100);
      }
    };

    const updateRecommendation = () => {
      const context = window.mppDynamicContent.getContext();
      const recommendedPackageId = window.mppDynamicContent.getPersonalizedRecommendation();
      
      if (!recommendedPackageId) {
        recommendationElement.style.display = 'none';
        return;
      }

      // Update recommendation content based on context
      updateRecommendationContent(context, recommendedPackageId);
    };

    const updateRecommendationContent = (context, packageId) => {
      const titleElement = recommendationElement.querySelector('.recommendation-title');
      const messageElement = recommendationElement.querySelector('.recommendation-message');
      const packageNameElement = recommendationElement.querySelector('.recommended-package-name');
      const packageReasonElement = recommendationElement.querySelector('.recommended-package-reason');

      // Personalize title based on context
      if (context.behavior.visitCount > 1) {
        titleElement.textContent = 'Welcome Back! Here\'s What We Suggest';
      } else if (context.device.type === 'mobile') {
        titleElement.textContent = 'Perfect for Mobile Users';
      }

      // Personalize message based on location
      if (context.location?.city) {
        messageElement.textContent = `Popular choice in ${context.location.city}`;
      } else if (context.device.type === 'mobile') {
        messageElement.textContent = 'Optimized for your device and space';
      }

      // Update package-specific content
      const packageData = getPackageData(packageId);
      if (packageData) {
        packageNameElement.textContent = packageData.name;
        packageReasonElement.textContent = getPersonalizedReason(context, packageId);
      }
    };

    const getPackageData = (packageId) => {
      const packages = {
        'harvest-essentials': { name: 'Harvest Essentials', price: '$349' },
        'pumpkin-charm': { name: 'Pumpkin Charm', price: '$599' },
        'fall-favorites': { name: 'Fall Favorites', price: '$799' },
        'autumn-splendor': { name: 'Autumn Splendor', price: '$999' }
      };
      return packages[packageId];
    };

    const getPersonalizedReason = (context, packageId) => {
      if (context.device.type === 'mobile' && packageId === 'harvest-essentials') {
        return 'Perfect for smaller spaces';
      }
      if (context.behavior.visitCount > 1) {
        return 'Based on your previous interest';
      }
      if (context.location?.state === 'Utah') {
        return 'Most popular in Utah';
      }
      return 'Recommended for you';
    };

    checkDynamicContent();
  });
</script>

<style>
  .personalized-recommendation {
    animation: slideIn 0.5s ease-out;
  }

  @keyframes slideIn {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .recommended-package-preview:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease-in-out;
  }

  .recommended-package-cta:hover {
    transform: translateY(-1px);
    transition: all 0.2s ease-in-out;
  }

  /* Mobile optimizations */
  @media (max-width: 640px) {
    .flex.gap-3 {
      flex-direction: column;
      gap: 0.5rem;
    }
    
    .recommended-package-cta {
      text-align: center;
    }
  }
</style>
