/** @type {import('tailwindcss').Config} */
export default {
  content: ['./src/**/*.{astro,html,js,jsx,md,mdx,svelte,ts,tsx,vue}'],
  
  // Mobile-first approach - all utilities are mobile by default
  theme: {
    extend: {
      // Color Hunt Palette: Autumn Farmhouse (#BF9264 #6F826A #BBD8A3 #F0F1C5)
      colors: {
        primary: {
          50: '#faf8f5',
          100: '#f5f1ea',
          200: '#ebe3d5',
          300: '#e0d4c0',
          400: '#d6c6ab',
          500: '#BF9264', // Warm brown/tan - main brand color
          600: '#ab8258',
          700: '#97724c',
          800: '#836240',
          900: '#6f5234',
          950: '#5b4228',
        },
        secondary: {
          50: '#f6f7f6',
          100: '#edefed',
          200: '#dbdfdb',
          300: '#c9cfc9',
          400: '#b7bfb7',
          500: '#6F826A', // Sage green - complementary accent
          600: '#63755e',
          700: '#576852',
          800: '#4b5b46',
          900: '#3f4e3a',
          950: '#33412e',
        },
        accent: {
          50: '#f8faf6',
          100: '#f1f5ed',
          200: '#e3ebdb',
          300: '#d5e1c9',
          400: '#c7d7b7',
          500: '#BBD8A3', // Light green - fresh accent
          600: '#a9c993',
          700: '#97ba83',
          800: '#85ab73',
          900: '#739c63',
          950: '#618d53',
        },
        light: {
          50: '#fefffe',
          100: '#fdfefd',
          200: '#fbfdfb',
          300: '#f9fcf9',
          400: '#f7fbf7',
          500: '#F0F1C5', // Cream/light yellow - soft background
          600: '#e8e9b7',
          700: '#e0e1a9',
          800: '#d8d99b',
          900: '#d0d18d',
          950: '#c8c97f',
        },
        dark: {
          50: '#f5f3f0',
          100: '#ebe7e0',
          200: '#d7cfc1',
          300: '#c3b7a2',
          400: '#af9f83',
          500: '#8b7355', // Warm Dark Gray
          600: '#7d6650',
          700: '#6f594b',
          800: '#614c46',
          900: '#533f41',
          950: '#3e2723', // Dark Brown - rich earth tone
        },
        warm: {
          50: '#fefcf8', // Warm White
          100: '#faf7f2', // Warm Cream
          200: '#f5f2ed', // Warm Light Gray
          300: '#f5f5dc', // Beige - warm neutral
          400: '#ede7dd', // Soft Beige Gray
          500: '#d4c4b0', // Warm Medium Gray
          600: '#a68b5b', // Taupe
          700: '#9caf88', // Sage Green - muted natural green
          800: '#a0522d', // Sienna - rustic red-brown
          900: '#cd5c5c', // Indian Red - warm earthy red
          950: '#daa520', // Goldenrod - harvest yellow
        }
      },
      
      // Typography scale optimized for mobile readability
      fontFamily: {
        sans: ['Poppins', 'system-ui', 'sans-serif'],
        serif: ['Playfair Display', 'serif'],
      },
      
      fontSize: {
        'xs': ['0.75rem', { lineHeight: '1rem' }],
        'sm': ['0.875rem', { lineHeight: '1.25rem' }],
        'base': ['1rem', { lineHeight: '1.5rem' }],
        'lg': ['1.125rem', { lineHeight: '1.75rem' }],
        'xl': ['1.25rem', { lineHeight: '1.75rem' }],
        '2xl': ['1.5rem', { lineHeight: '2rem' }],
        '3xl': ['1.875rem', { lineHeight: '2.25rem' }],
        '4xl': ['2.25rem', { lineHeight: '2.5rem' }],
        '5xl': ['3rem', { lineHeight: '1' }],
        '6xl': ['3.75rem', { lineHeight: '1' }],
        '7xl': ['4.5rem', { lineHeight: '1' }],
        '8xl': ['6rem', { lineHeight: '1' }],
        '9xl': ['8rem', { lineHeight: '1' }],
      },
      
      // Spacing scale for consistent mobile-first design
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
        '128': '32rem',
      },
      
      // Mobile-optimized breakpoints
      screens: {
        'xs': '475px',
        'sm': '640px',
        'md': '768px',
        'lg': '1024px',
        'xl': '1280px',
        '2xl': '1536px',
      },
      
      // Animation and transitions optimized for mobile performance
      animation: {
        'fade-in': 'fadeIn 0.3s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'slide-down': 'slideDown 0.3s ease-out',
        'scale-in': 'scaleIn 0.2s ease-out',
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
      },
      
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(20px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        slideDown: {
          '0%': { transform: 'translateY(-20px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        scaleIn: {
          '0%': { transform: 'scale(0.9)', opacity: '0' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
      },
      
      // Box shadows optimized for mobile
      boxShadow: {
        'mobile': '0 2px 8px rgba(0, 0, 0, 0.1)',
        'mobile-lg': '0 4px 16px rgba(0, 0, 0, 0.15)',
        'mobile-xl': '0 8px 32px rgba(0, 0, 0, 0.2)',
      },
      
      // Border radius scale
      borderRadius: {
        'xl': '0.75rem',
        '2xl': '1rem',
        '3xl': '1.5rem',
      },
      
      // Z-index scale for layering
      zIndex: {
        '60': '60',
        '70': '70',
        '80': '80',
        '90': '90',
        '100': '100',
      },
    },
  },
  
  plugins: [
    require('@tailwindcss/typography'),
    require('@tailwindcss/forms'),
    
    // Custom plugin for mobile-first utilities
    function({ addUtilities, theme }) {
      const newUtilities = {
        // Touch-friendly tap targets
        '.tap-target': {
          minHeight: '44px',
          minWidth: '44px',
        },
        
        // Safe area utilities for mobile devices
        '.safe-top': {
          paddingTop: 'env(safe-area-inset-top)',
        },
        '.safe-bottom': {
          paddingBottom: 'env(safe-area-inset-bottom)',
        },
        '.safe-left': {
          paddingLeft: 'env(safe-area-inset-left)',
        },
        '.safe-right': {
          paddingRight: 'env(safe-area-inset-right)',
        },
        
        // Mobile-optimized text shadows
        '.text-shadow-sm': {
          textShadow: '0 1px 2px rgba(0, 0, 0, 0.5)',
        },
        '.text-shadow': {
          textShadow: '0 2px 4px rgba(0, 0, 0, 0.5)',
        },
        '.text-shadow-lg': {
          textShadow: '0 4px 8px rgba(0, 0, 0, 0.5)',
        },
        
        // Performance optimized transforms
        '.gpu-accelerated': {
          transform: 'translateZ(0)',
          willChange: 'transform',
        },
      }
      
      addUtilities(newUtilities)
    }
  ],
}
