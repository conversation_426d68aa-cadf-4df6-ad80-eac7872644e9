---
// Reusable Form Field component
// Eliminates duplicate form field patterns across components

import { getFormFieldAttributes, getFormFieldClasses, getValidationAttributes, getModelAttribute, getErrorAttributes, US_STATES } from '../utils/form-components';

export interface Props {
  fieldName: string;
  label?: string;
  type?: string;
  placeholder?: string;
  required?: boolean;
  autocomplete?: string;
  maxlength?: number;
  rows?: number;
  options?: Array<{ value: string; label: string }>;
  validation?: string[];
  variant?: 'default' | 'inline' | 'compact';
  className?: string;
}

const {
  fieldName,
  label,
  type,
  placeholder,
  required,
  autocomplete,
  maxlength,
  rows,
  options,
  validation,
  variant = 'default',
  className = ''
} = Astro.props;

// Get field configuration with overrides
const fieldProps = getFormFieldAttributes(fieldName, {
  label,
  type,
  placeholder,
  required,
  autocomplete,
  maxlength,
  rows,
  options,
  validation,
  variant
});

// Get styling classes
const classes = getFormFieldClasses(variant);

// Get Alpine.js attributes
const validationAttr = getValidationAttributes(fieldName, fieldProps.validation);
const modelAttr = getModelAttribute(fieldName);
const errorAttrs = getErrorAttributes(fieldName);

// Special handling for state dropdown
const stateOptions = fieldName === 'state' ? US_STATES : (fieldProps.options || []);
---

<div class:list={[classes.container, className]}>
  <label for={fieldProps.id} class={classes.label}>
    {fieldProps.label}{fieldProps.required ? ' *' : ''}
  </label>
  
  {fieldProps.type === 'textarea' ? (
    <textarea
      id={fieldProps.id}
      name={fieldProps.name}
      x-model={`formData.${fieldName}`}
      {...{[validationAttr.split('=')[0].substring(1)]: validationAttr.split('=')[1].slice(1, -1)}}
      class={classes.input}
      placeholder={fieldProps.placeholder}
      rows={fieldProps.rows || 4}
      maxlength={fieldProps.maxlength}
      autocomplete={fieldProps.autocomplete}
      {...(fieldProps.required ? { required: true } : {})}
    ></textarea>
  ) : fieldProps.type === 'select' ? (
    <select
      id={fieldProps.id}
      name={fieldProps.name}
      x-model={`formData.${fieldName}`}
      {...{[validationAttr.split('=')[0].substring(1)]: validationAttr.split('=')[1].slice(1, -1)}}
      class={classes.input}
      autocomplete={fieldProps.autocomplete}
      {...(fieldProps.required ? { required: true } : {})}
    >
      {stateOptions.map(option => (
        <option value={option.value}>{option.label}</option>
      ))}
    </select>
  ) : (
    <input
      type={fieldProps.type}
      id={fieldProps.id}
      name={fieldProps.name}
      x-model={`formData.${fieldName}`}
      {...{[validationAttr.split('=')[0].substring(1)]: validationAttr.split('=')[1].slice(1, -1)}}
      class={classes.input}
      placeholder={fieldProps.placeholder}
      maxlength={fieldProps.maxlength}
      autocomplete={fieldProps.autocomplete}
      {...(fieldProps.required ? { required: true } : {})}
    />
  )}
  
  <p 
    x-show={`errors.${fieldName}`} 
    x-text={`errors.${fieldName}`} 
    class={classes.error}
    style="display: none;"
  ></p>
</div>

<script>
  // Enhanced form field behavior
  document.addEventListener('DOMContentLoaded', () => {
    // Prevent form submission on Enter key in form fields
    const formFields = document.querySelectorAll('input, select, textarea');
    
    formFields.forEach(field => {
      field.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' && field.type !== 'textarea') {
          e.preventDefault();
          
          // Move to next form field
          const formElements = Array.from(field.form?.elements || []);
          const currentIndex = formElements.indexOf(field);
          const nextField = formElements[currentIndex + 1];
          
          if (nextField && (nextField.type !== 'submit' && nextField.type !== 'button')) {
            nextField.focus();
          }
        }
      });
    });
    
    // Auto-format phone numbers
    const phoneFields = document.querySelectorAll('input[type="tel"]');
    phoneFields.forEach(field => {
      field.addEventListener('input', (e) => {
        let value = e.target.value.replace(/\D/g, '');
        if (value.length >= 6) {
          value = value.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
        } else if (value.length >= 3) {
          value = value.replace(/(\d{3})(\d{0,3})/, '($1) $2');
        }
        e.target.value = value;
      });
    });
    
    // Auto-format ZIP codes
    const zipFields = document.querySelectorAll('input[name="zipCode"]');
    zipFields.forEach(field => {
      field.addEventListener('input', (e) => {
        let value = e.target.value.replace(/\D/g, '');
        if (value.length > 5) {
          value = value.substring(0, 5);
        }
        e.target.value = value;
      });
    });
  });
</script>
