{"buildCommand": "npm run build", "outputDirectory": "dist", "framework": "astro", "functions": {"api/*.js": {"runtime": "@vercel/node"}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}]}, {"source": "/images/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*\\.css)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}, {"key": "Content-Type", "value": "text/css; charset=utf-8"}]}, {"source": "/(.*\\.js)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}, {"key": "Content-Type", "value": "application/javascript; charset=utf-8"}]}], "rewrites": [{"source": "/api/(.*)", "destination": "/api/$1"}]}