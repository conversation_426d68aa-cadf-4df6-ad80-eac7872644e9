// Performance monitoring API endpoint
// Collects and logs performance metrics from the client

import type { APIRoute } from 'astro';

// Performance metrics interface
interface PerformanceMetrics {
  url: string;
  timestamp: number;
  metrics: {
    lcp?: number;
    fid?: number;
    cls?: number;
    fcp?: number;
    navigationTiming?: any;
    resources?: any;
    longTasks?: any[];
  };
  device: {
    userAgent: string;
    viewport: { width: number; height: number };
    screen: { width: number; height: number; pixelRatio: number };
    connection?: any;
    memory?: number;
    cores?: number;
  };
  performance: {
    score: number;
    grade: string;
  };
}

// Log performance data (in production, send to analytics service)
function logPerformanceMetrics(data: PerformanceMetrics) {
  console.log('📊 Performance Metrics Received:', {
    url: data.url,
    timestamp: new Date(data.timestamp).toISOString(),
    coreWebVitals: {
      lcp: data.metrics.lcp ? `${data.metrics.lcp}ms` : 'N/A',
      fid: data.metrics.fid ? `${data.metrics.fid}ms` : 'N/A',
      cls: data.metrics.cls || 'N/A',
      fcp: data.metrics.fcp ? `${data.metrics.fcp}ms` : 'N/A'
    },
    performanceScore: `${data.performance.score}/100 (${data.performance.grade})`,
    device: {
      viewport: `${data.device.viewport.width}x${data.device.viewport.height}`,
      screen: `${data.device.screen.width}x${data.device.screen.height}`,
      pixelRatio: data.device.screen.pixelRatio,
      connection: data.device.connection?.effectiveType || 'Unknown'
    }
  });
  
  // In production, you would send this data to:
  // - Google Analytics 4
  // - DataDog
  // - New Relic
  // - Custom analytics service
  // - Database for analysis
}

// Analyze performance and provide recommendations
function analyzePerformance(metrics: PerformanceMetrics['metrics']) {
  const recommendations = [];
  
  // LCP analysis
  if (metrics.lcp) {
    if (metrics.lcp > 4000) {
      recommendations.push('LCP is poor (>4s). Consider optimizing images and reducing server response time.');
    } else if (metrics.lcp > 2500) {
      recommendations.push('LCP needs improvement (>2.5s). Consider image optimization and preloading critical resources.');
    }
  }
  
  // FID analysis
  if (metrics.fid) {
    if (metrics.fid > 300) {
      recommendations.push('FID is poor (>300ms). Consider reducing JavaScript execution time and using code splitting.');
    } else if (metrics.fid > 100) {
      recommendations.push('FID needs improvement (>100ms). Consider optimizing JavaScript and reducing main thread blocking.');
    }
  }
  
  // CLS analysis
  if (metrics.cls) {
    if (metrics.cls > 0.25) {
      recommendations.push('CLS is poor (>0.25). Consider setting image dimensions and avoiding dynamic content insertion.');
    } else if (metrics.cls > 0.1) {
      recommendations.push('CLS needs improvement (>0.1). Consider stabilizing layout shifts and reserving space for dynamic content.');
    }
  }
  
  return recommendations;
}

// Main API handler
export const POST: APIRoute = async ({ request }) => {
  try {
    // Parse performance data
    const data: PerformanceMetrics = await request.json();
    
    // Validate required fields
    if (!data.url || !data.timestamp || !data.metrics) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Missing required performance data'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Log performance metrics
    logPerformanceMetrics(data);
    
    // Analyze performance and get recommendations
    const recommendations = analyzePerformance(data.metrics);
    
    // In development, provide detailed feedback
    if (import.meta.env.DEV) {
      console.log('🔍 Performance Analysis:', {
        score: data.performance.score,
        grade: data.performance.grade,
        recommendations: recommendations.length > 0 ? recommendations : ['Performance looks good! 🎉']
      });
    }
    
    // Return success response
    return new Response(JSON.stringify({
      success: true,
      message: 'Performance metrics recorded',
      analysis: {
        score: data.performance.score,
        grade: data.performance.grade,
        recommendations: recommendations
      }
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
    
  } catch (error) {
    console.error('❌ Performance API error:', error);
    
    return new Response(JSON.stringify({
      success: false,
      error: 'Failed to process performance data'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

// Handle other HTTP methods
export const GET: APIRoute = async () => {
  return new Response(JSON.stringify({
    message: 'Performance monitoring endpoint',
    usage: 'POST performance metrics to this endpoint',
    example: {
      url: 'https://example.com',
      timestamp: Date.now(),
      metrics: {
        lcp: 1200,
        fid: 50,
        cls: 0.05,
        fcp: 800
      },
      device: {
        viewport: { width: 390, height: 844 },
        screen: { width: 390, height: 844, pixelRatio: 3 }
      },
      performance: {
        score: 95,
        grade: 'A'
      }
    }
  }), {
    status: 200,
    headers: { 'Content-Type': 'application/json' }
  });
};

export const PUT = GET;
export const DELETE = GET;
export const PATCH = GET;
