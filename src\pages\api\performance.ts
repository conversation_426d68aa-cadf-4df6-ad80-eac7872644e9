// Simplified performance monitoring API endpoint
// Following YAGNI principles - only essential functionality

import type { APIRoute } from 'astro';
import { createSuccessResponse, createErrorResponse, checkRateLimit, safeParseJson } from '../../utils/api';

// Basic performance metrics interface
interface BasicPerformanceMetrics {
  url: string;
  timestamp: number;
  lcp?: number;
  fid?: number;
  cls?: number;
}

// Main API handler
export const POST: APIRoute = async ({ request, clientAddress }) => {
  try {
    // Rate limiting for performance data
    const rateLimitResult = await checkRateLimit(clientAddress, 'performance-metrics', 10, 60); // 10 requests per minute
    if (!rateLimitResult.allowed) {
      return createErrorResponse('Too many performance metric submissions', 429);
    }

    // Parse and validate request
    const parseResult = await safeParseJson(request);
    if (!parseResult.success) {
      return createErrorResponse('Invalid request format', 400);
    }

    const data: BasicPerformanceMetrics = parseResult.data;

    // Basic validation
    if (!data.url || !data.timestamp) {
      return createErrorResponse('Missing required fields: url, timestamp', 400);
    }

    // Simple logging (in production, send to analytics service)
    if (import.meta.env.DEV) {
      console.log('📊 Performance Metrics:', {
        url: data.url,
        timestamp: new Date(data.timestamp).toISOString(),
        lcp: data.lcp ? `${data.lcp}ms` : 'N/A',
        fid: data.fid ? `${data.fid}ms` : 'N/A',
        cls: data.cls || 'N/A'
      });
    }

    return createSuccessResponse({
      message: 'Performance metrics recorded'
    });

  } catch (error) {
    console.error('Performance API error:', error);
    return createErrorResponse('Failed to process performance data', 500);
  }
};
