// Core business types for Mountain Porch Pumpkins
// Following SOLID principles with clear interfaces

export interface Package {
  id: string;
  name: string;
  price: number;
  description: string;
  image: string;
  features: string[];
  category: 'residential' | 'business' | 'event';
  size: 'small' | 'medium' | 'large' | 'custom';
  quantity?: string; // Optional quantity indicator (e.g., "8-12", "15-18", "20+")
  categorizedFeatures?: {
    whatYouGet?: string[];
    howWeDeliver?: string[];
    perfectFor?: string[];
  };
}

export interface CustomerInfo {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
}

export interface BusinessCustomerInfo extends CustomerInfo {
  businessName: string;
  businessType: string;
  contactTitle?: string;
}

export interface DeliveryDetails {
  deliveryDate: string;
  setupTime: 'morning' | 'afternoon' | 'evening';
  specialRequests?: string;
  accessInstructions?: string;
}

export interface OrderDetails {
  package: Package;
  customer: CustomerInfo | BusinessCustomerInfo;
  delivery: DeliveryDetails;
  totalAmount: number;
  paymentStatus: 'pending' | 'processing' | 'completed' | 'failed';
}

// Photo showcase types
export interface ShowcasePhoto {
  image: string;
  title: string;
  package: string;
  location: string;
  alt: string;
  category?: 'residential' | 'business' | 'event';
}

// Form validation types
export interface ValidationResult {
  isValid: boolean;
  errors: Record<string, string>;
}

export interface FormField {
  name: string;
  value: string;
  required: boolean;
  type: 'text' | 'email' | 'tel' | 'date' | 'select' | 'textarea';
  validation?: (value: string) => ValidationResult;
}

// API response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaymentIntentResponse {
  clientSecret: string;
  paymentIntentId: string;
}

// Component prop types
export interface BaseComponentProps {
  class?: string;
  id?: string;
}

export interface ButtonProps extends BaseComponentProps {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  type?: 'button' | 'submit' | 'reset';
}

export interface ModalProps extends BaseComponentProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
}

// Service area types
export interface ServiceArea {
  state: string;
  cities: string[];
  regions: string[];
  isActive: boolean;
}

// Performance monitoring types
export interface PerformanceMetrics {
  lcp?: number; // Largest Contentful Paint
  fid?: number; // First Input Delay
  cls?: number; // Cumulative Layout Shift
  fcp?: number; // First Contentful Paint
  ttfb?: number; // Time to First Byte
}

// Alpine.js store types
export interface AppStore {
  // Navigation state
  mobileMenuOpen: boolean;

  // Package selection
  selectedPackage: Package | null;
  packageModalOpen: boolean;

  // Form state
  currentStep: number;
  formData: Partial<OrderDetails>;
  formErrors: Record<string, string>;

  // UI state
  loading: boolean;
  notifications: Notification[];

  // Navigation methods
  toggleMobileMenu(): void;
  closeMobileMenu(): void;
  scrollToSection(sectionId: string): void;
  selectPackageInForm(packageId: string): void;
}

export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
  actions?: NotificationAction[];
}

export interface NotificationAction {
  label: string;
  action: () => void;
  style?: 'primary' | 'secondary';
}

// Utility types for better type safety
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;
export type OptionalFields<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

// Environment configuration
export interface EnvironmentConfig {
  isDevelopment: boolean;
  isProduction: boolean;
  apiBaseUrl: string;
  stripePublishableKey: string;
  enableAnalytics: boolean;
  enableServiceWorker: boolean;
}
