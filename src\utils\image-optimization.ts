// Shared image optimization utilities
// Eliminates duplication between OptimizedImage and PhotoCarousel components

export interface ImageOptimizationConfig {
  width?: number;
  height?: number;
  quality?: number;
  format?: 'webp' | 'avif' | 'jpeg' | 'png';
  sizes?: string;
  loading?: 'lazy' | 'eager';
  decoding?: 'async' | 'sync' | 'auto';
  priority?: boolean;
  placeholder?: 'blur' | 'none';
}

export interface ResponsiveImageData {
  src: string;
  srcset: string;
  placeholderSrc: string;
  actualLoading: 'lazy' | 'eager';
  actualDecoding: 'async' | 'sync' | 'auto';
  aspectRatio?: string;
}

/**
 * Standard responsive breakpoint widths
 */
export const RESPONSIVE_WIDTHS = [320, 640, 768, 1024, 1280, 1920];

/**
 * Generate optimized image URL for external services (like Unsplash)
 */
export function generateImageUrl(
  src: string, 
  width: number, 
  quality: number = 80, 
  format: string = 'webp'
): string {
  // For external URLs (like Unsplash), use their optimization parameters
  if (src.startsWith('http')) {
    const url = new URL(src);
    url.searchParams.set('w', width.toString());
    url.searchParams.set('q', quality.toString());
    url.searchParams.set('fm', format);
    url.searchParams.set('fit', 'crop');
    url.searchParams.set('crop', 'center');
    return url.toString();
  }
  
  // For local images, would use Astro's image optimization
  // This is a placeholder for local image handling
  return src;
}

/**
 * Generate srcset for responsive images
 */
export function generateSrcset(
  src: string, 
  quality: number = 80, 
  format: string = 'webp',
  widths: number[] = RESPONSIVE_WIDTHS
): string {
  if (!src.startsWith('http')) {
    return ''; // Don't generate srcset for local images without processing
  }
  
  return widths
    .map(w => `${generateImageUrl(src, w, quality, format)} ${w}w`)
    .join(', ');
}

/**
 * Generate placeholder image for blur effect
 */
export function generatePlaceholder(
  src: string, 
  placeholder: 'blur' | 'none' = 'blur'
): string {
  if (placeholder === 'none') {
    return '';
  }
  
  if (src.startsWith('http')) {
    return generateImageUrl(src, 20, 20, 'jpeg');
  }
  
  // Use original image for local files
  return src;
}

/**
 * Generate SVG placeholder for lazy loading
 */
export function generateSvgPlaceholder(): string {
  return "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1 1'%3E%3C/svg%3E";
}

/**
 * Calculate aspect ratio string
 */
export function calculateAspectRatio(width?: number, height?: number): string | undefined {
  if (width && height) {
    return `${width}/${height}`;
  }
  return undefined;
}

/**
 * Determine loading strategy based on priority
 */
export function determineLoadingStrategy(
  priority: boolean = false,
  loading: 'lazy' | 'eager' = 'lazy',
  decoding: 'async' | 'sync' | 'auto' = 'async'
): { loading: 'lazy' | 'eager'; decoding: 'async' | 'sync' | 'auto' } {
  return {
    loading: priority ? 'eager' : loading,
    decoding: priority ? 'sync' : decoding
  };
}

/**
 * Generate complete responsive image data
 * Consolidates all image optimization logic in one place
 */
export function generateResponsiveImageData(
  src: string,
  config: ImageOptimizationConfig = {}
): ResponsiveImageData {
  const {
    width = 800,
    height,
    quality = 80,
    format = 'webp',
    sizes = '100vw',
    loading = 'lazy',
    decoding = 'async',
    priority = false,
    placeholder = 'blur'
  } = config;

  const srcset = generateSrcset(src, quality, format);
  const placeholderSrc = generatePlaceholder(src, placeholder);
  const { loading: actualLoading, decoding: actualDecoding } = determineLoadingStrategy(
    priority, 
    loading, 
    decoding
  );
  const aspectRatio = calculateAspectRatio(width, height);

  return {
    src: generateImageUrl(src, width, quality, format),
    srcset,
    placeholderSrc,
    actualLoading,
    actualDecoding,
    aspectRatio
  };
}

/**
 * Common image configurations for different use cases
 */
export const IMAGE_CONFIGS = {
  hero: {
    width: 1920,
    height: 1080,
    quality: 85,
    format: 'webp' as const,
    sizes: '100vw',
    priority: true,
    loading: 'eager' as const
  },
  carousel: {
    width: 1200,
    height: 675,
    quality: 80,
    format: 'webp' as const,
    sizes: '100vw',
    priority: false,
    loading: 'lazy' as const
  },
  thumbnail: {
    width: 200,
    height: 200,
    quality: 75,
    format: 'webp' as const,
    sizes: '(max-width: 640px) 25vw, (max-width: 768px) 16vw, 12vw',
    priority: false,
    loading: 'lazy' as const
  },
  card: {
    width: 600,
    height: 400,
    quality: 80,
    format: 'webp' as const,
    sizes: '(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw',
    priority: false,
    loading: 'lazy' as const
  }
} as const;
