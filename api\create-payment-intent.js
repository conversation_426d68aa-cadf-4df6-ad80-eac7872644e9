const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method !== 'POST') {
    res.setHeader('Allow', 'POST');
    res.status(405).end('Method Not Allowed');
    return;
  }

  try {
    const { 
      amount, 
      currency = 'usd',
      customerInfo,
      packageDetails 
    } = req.body;

    // Validate required fields
    if (!amount || !customerInfo) {
      return res.status(400).json({ 
        error: 'Missing required fields: amount and customerInfo' 
      });
    }

    // Create payment intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(amount * 100), // Convert to cents
      currency: currency,
      metadata: {
        customer_name: `${customerInfo.firstName} ${customerInfo.lastName}`,
        customer_email: customerInfo.email,
        customer_phone: customerInfo.phone,
        delivery_address: customerInfo.address,
        package_selection: packageDetails.package,
        delivery_date: packageDetails.deliveryDate,
        special_requests: packageDetails.specialRequests || 'None'
      },
      receipt_email: customerInfo.email,
      description: `Mountain Porch Pumpkins - ${packageDetails.package}`
    });

    res.status(200).json({
      clientSecret: paymentIntent.client_secret,
      paymentIntentId: paymentIntent.id
    });

  } catch (error) {
    console.error('Payment intent creation failed:', error);
    res.status(500).json({ 
      error: 'Payment processing failed. Please try again.' 
    });
  }
}
