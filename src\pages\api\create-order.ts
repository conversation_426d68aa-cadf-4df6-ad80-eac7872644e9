// Order creation API endpoint with Stripe integration
import type { APIRoute } from 'astro';

export const POST: APIRoute = async ({ request }) => {
  try {
    const body = await request.json();
    const { formData, formMode } = body;

    // Validate required fields
    if (!formData || formMode !== 'order') {
      return new Response(JSON.stringify({ 
        success: false, 
        error: 'Invalid order data' 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Package pricing (in cents for Stripe)
    const packagePricing = {
      'harvest-essentials': 34900, // $349.00
      'pumpkin-charm': 59900,      // $599.00
      'fall-favorites': 79900,     // $799.00
      'autumn-splendor': 99900     // $999.00
    };

    const amount = packagePricing[formData.package as keyof typeof packagePricing];
    
    if (!amount) {
      return new Response(JSON.stringify({ 
        success: false, 
        error: 'Invalid package selected' 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // In a real implementation, you would:
    // 1. Create a Stripe Payment Intent
    // 2. Save order to database
    // 3. Send confirmation emails
    // 4. Return client secret for payment completion

    // For now, return a mock response
    const mockClientSecret = `pi_mock_${Date.now()}_secret_mock`;

    console.log('Order creation request:', {
      timestamp: new Date().toISOString(),
      customer: {
        name: `${formData.firstName} ${formData.lastName}`,
        email: formData.email,
        phone: formData.phone,
        address: `${formData.streetAddress}, ${formData.city}, ${formData.state} ${formData.zipCode}`
      },
      package: formData.package,
      amount: amount / 100, // Convert back to dollars for logging
      message: formData.message
    });

    return new Response(JSON.stringify({
      success: true,
      clientSecret: mockClientSecret,
      orderId: `ORDER-${Date.now()}`,
      amount: amount
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Order creation error:', error);
    return new Response(JSON.stringify({ 
      success: false, 
      error: 'Internal server error' 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
