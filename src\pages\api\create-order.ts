// Order creation API endpoint with Stripe integration
import type { APIRoute } from 'astro';
import {
  validateAndSanitizeForm,
  CONTACT_FORM_RULES
} from '../../utils/validation';
import {
  createSuccessResponse,
  createValidationErrorResponse,
  createServerErrorResponse,
  safeParseJson,
  extractClientMetadata,
  logApiRequest,
  checkRateLimit,
  createRateLimitResponse
} from '../../utils/api';
import {
  getPackagePricingCents,
  validatePackageSelection,
  getPackageById
} from '../../utils/packages';

export const POST: APIRoute = async ({ request }) => {
  try {
    // Extract client metadata for rate limiting
    const metadata = extractClientMetadata(request);

    // Check rate limit (5 orders per minute per IP)
    const rateLimitCheck = checkRateLimit(metadata.ip, 5, 60000);
    if (!rateLimitCheck.allowed) {
      return createRateLimitResponse(rateLimitCheck.resetTime);
    }

    // Parse request body safely
    const parseResult = await safeParseJson(request);
    if (!parseResult.success) {
      return createValidationErrorResponse({ json: parseResult.error });
    }

    const { formData, formMode } = parseResult.data;

    // Log API request for debugging
    logApiRequest('POST', '/api/create-order', { formMode, package: formData?.package }, metadata);

    // Validate order mode
    if (formMode !== 'order') {
      return createValidationErrorResponse({ formMode: 'Invalid form mode for order creation' });
    }

    // Validate and sanitize form data
    const validation = validateAndSanitizeForm(formData, CONTACT_FORM_RULES);
    if (!validation.isValid) {
      return createValidationErrorResponse(validation.errors);
    }

    const sanitizedData = validation.sanitizedData;

    // Validate package selection
    const packageValidation = validatePackageSelection(sanitizedData.package);
    if (!packageValidation.isValid) {
      return createValidationErrorResponse({ package: packageValidation.error });
    }

    // Get package pricing
    const amount = getPackagePricingCents(sanitizedData.package);
    if (!amount) {
      return createValidationErrorResponse({ package: 'Package does not support direct ordering' });
    }

    // Get package details for order
    const packageDetails = getPackageById(sanitizedData.package);

    // In a real implementation, you would:
    // 1. Create a Stripe Payment Intent
    // 2. Save order to database
    // 3. Send confirmation emails
    // 4. Return client secret for payment completion

    // For now, return a mock response
    const mockClientSecret = `pi_mock_${Date.now()}_secret_mock`;
    const orderId = `ORDER-${Date.now()}`;

    console.log('✅ Order creation request:', {
      orderId,
      timestamp: new Date().toISOString(),
      customer: {
        name: `${sanitizedData.firstName} ${sanitizedData.lastName}`,
        email: sanitizedData.email,
        phone: sanitizedData.phone
      },
      package: {
        id: sanitizedData.package,
        name: packageDetails?.name,
        price: amount / 100 // Convert back to dollars for logging
      }
    });

    // Return success response using shared utility
    return createSuccessResponse({
      clientSecret: mockClientSecret,
      orderId,
      amount,
      package: {
        id: sanitizedData.package,
        name: packageDetails?.name,
        price: amount / 100
      }
    }, 'Order created successfully');

  } catch (error) {
    console.error('❌ Order creation error:', error);
    return createServerErrorResponse('Failed to create order. Please try again.');
  }
};
