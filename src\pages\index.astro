---
import BaseLayout from '../layouts/BaseLayout.astro';
import Navigation from '../components/Navigation.astro';
import PackageCard from '../components/PackageCard.astro';
import PhotoCarousel from '../components/PhotoCarousel.astro';
import ContactForm from '../components/ContactForm.astro';
import UniversalFeatures from '../components/UniversalFeatures.astro';
import type { Package, ShowcasePhoto } from '../types';

const title = "Pumpkin Delivery Utah Idaho Wyoming | Fall Display Service Salt Lake City Jackson Hole Park City";
const description = "Professional pumpkin delivery & display service in Utah, Idaho & Wyoming. Serving Salt Lake City, Jackson Hole, Park City, Sun Valley, Boise. Setup begins Sept 21st - Book now!";

// Package data following our type interface
const packages: Package[] = [
  {
    id: 'harvest-essentials',
    name: 'Harvest Essentials',
    price: 349,
    description: 'Cozy porch perfection',
    image: '/images/Package 1 displayed.jpg',
    features: [
      'Seasonal decorative elements',
      '2-week guarantee',
      'Covers 4-10 sq ft'
    ],
    category: 'residential',
    size: 'small',
    quantity: '8-12',
    categorizedFeatures: {
      whatYouGet: ['Perfect porch coverage', 'Beautiful seasonal variety'],
      howWeDeliver: ['Stays fresh 2+ weeks', 'Hassle-free setup'],
      perfectFor: ['Cozy apartment living', 'Small space magic']
    }
  },
  {
    id: 'pumpkin-charm',
    name: 'Pumpkin Charm',
    price: 599,
    description: 'Beautiful color combinations',
    image: '/images/package 2 displayed.jpg',
    features: [
      'Mini to large sizes',
      'Orange, white, green colors',
      '3-week guarantee',
      'Optional maintenance visit',
      'Covers 10-20 sq ft'
    ],
    category: 'residential',
    size: 'medium',
    quantity: '15-18',
    categorizedFeatures: {
      whatYouGet: ['Generous pumpkin variety', 'Stunning color palette', 'Multiple size options'],
      howWeDeliver: ['Extended freshness', 'Maintenance support', 'Worry-free installation'],
      perfectFor: ['Growing families', 'Color enthusiasts', 'Medium spaces']
    }
  },
  {
    id: 'fall-favorites',
    name: 'Fall Favorites',
    price: 799,
    description: 'Most popular choice',
    image: '/images/package 3 displayed.jpg',
    features: [
      'Enhanced styling elements',
      'Color coordination',
      'Design consultation',
      'Extended guarantee',
      'Maintenance included',
      'Perfect for entertaining'
    ],
    category: 'residential',
    size: 'large',
    quantity: '15-18',
    categorizedFeatures: {
      whatYouGet: ['Instagram-ready displays', 'Designer styling', 'Coordinated aesthetics'],
      howWeDeliver: ['Expert consultation', 'Season-long beauty', 'Ongoing care'],
      perfectFor: ['Entertainment spaces', 'Social gatherings', 'Photo memories']
    }
  },
  {
    id: 'autumn-splendor',
    name: 'Autumn Splendor',
    price: 999,
    description: 'Luxury estate displays',
    image: '/images/Custom Large Porch.jpg',
    features: [
      'Comprehensive decorative package',
      'Grand scale design',
      'Extended maintenance',
      'Perfect for luxury properties',
      'Photo-ready displays',
      'Covers extensive areas',
      'Seasonal refresh options'
    ],
    category: 'residential',
    size: 'large',
    quantity: '20+',
    categorizedFeatures: {
      whatYouGet: ['Spectacular abundance', 'Luxury-grade materials', 'Grand estate presence'],
      howWeDeliver: ['White-glove service', 'Magazine-worthy results', 'Seasonal evolution'],
      perfectFor: ['Luxury estates', 'Impressive entrances', 'Statement properties']
    }
  },
  {
    id: 'business-solutions',
    name: 'Business Solutions',
    price: 0,
    description: 'Commercial-grade displays',
    image: '/images/hotel display.jpg',
    features: [
      'Custom design consultation',
      'Commercial-grade materials',
      'Multiple location setup',
      'Regular maintenance',
      'Seasonal updates',
      'Brand-appropriate colors',
      'High-traffic durability'
    ],
    category: 'business',
    size: 'custom',
    categorizedFeatures: {
      whatYouGet: ['Business-grade quality', 'Brand-aligned aesthetics', 'Built for crowds'],
      howWeDeliver: ['Strategic planning', 'Multi-site coordination', 'Ongoing support', 'Fresh seasonal looks'],
      perfectFor: ['Customer-facing businesses', 'Brand enhancement', 'Professional atmosphere']
    }
  },
  {
    id: 'event-decor',
    name: 'Event Decor',
    price: 0,
    description: 'Custom event displays',
    image: '/images/Wreath.webp',
    features: [
      'Event-specific design',
      'Setup & breakdown',
      'Coordinated themes',
      'Photo-ready displays',
      'Timely delivery & pickup',
      'Event day support',
      'Wedding specialists',
      'Corporate expertise'
    ],
    category: 'event',
    size: 'custom',
    categorizedFeatures: {
      whatYouGet: ['Custom event magic', 'Theme coordination', 'Picture-perfect moments'],
      howWeDeliver: ['Stress-free logistics', 'Perfect timing', 'Day-of assistance'],
      perfectFor: ['Dream weddings', 'Corporate impressions', 'Memorable celebrations']
    }
  }
];

// Dynamic image loading from folders
import { readdir } from 'node:fs/promises';
import { join } from 'node:path';

// Function to get images from a folder
async function getImagesFromFolder(folderPath: string): Promise<string[]> {
  try {
    // Use the actual images directory path in public folder
    const fullPath = join(process.cwd(), 'public', 'images', folderPath);
    const files = await readdir(fullPath);
    return files
      .filter(file => /\.(jpg|jpeg|png|webp|gif)$/i.test(file))
      .map(file => `/images/${folderPath.toLowerCase()}/${file}`);
  } catch (error) {
    console.warn(`Could not read folder ${folderPath}:`, error);
    return [];
  }
}

// Function to generate photo data from image paths
function generatePhotoData(imagePaths: string[], category: 'residential' | 'business' | 'event'): ShowcasePhoto[] {
  return imagePaths.map((imagePath, index) => {
    const filename = imagePath.split('/').pop()?.replace(/\.[^/.]+$/, '') || '';
    const cleanName = filename.replace(/[-_]/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

    // Generate appropriate titles and locations based on category
    const locations = category === 'residential'
      ? ['🏔️ Jackson Hole, WY', '🏡 Salt Lake City, UT', '🌾 Boise, ID', '🍂 Logan, UT', '🌵 St. George, UT', '⛰️ Park City, UT']
      : ['🏨 Park City, UT', '⛰️ Sun Valley, ID', '🏜️ Moab, UT', '🏔️ Jackson Hole, WY', '🌲 Boise, ID'];

    const packages = category === 'residential'
      ? ['Harvest Essentials Package - $349', 'Pumpkin Charm Package - $599', 'Fall Favorites Package - $799', 'Autumn Splendor Package - $999']
      : ['Business Solutions Package', 'Event Decor Package'];

    const titles = category === 'residential'
      ? ['Mountain Estate', 'Family Home', 'Cottage Display', 'Farmhouse Setup', 'Desert Home', 'Luxury Porch']
      : ['Resort Entrance', 'Mountain Lodge', 'Adventure Lodge', 'Business Display', 'Event Setup', 'Commercial Space'];

    return {
      image: imagePath,
      title: titles[index % titles.length],
      package: packages[index % packages.length],
      location: locations[index % locations.length],
      alt: `Beautiful ${category} pumpkin display - Mountain Porch Pumpkins professional service`,
      category: category
    };
  });
}

// Load images dynamically from folders
const homeImagePaths = await getImagesFromFolder('Home');
const businessImagePaths = await getImagesFromFolder('Business');

// Generate photo data
const homePhotos: ShowcasePhoto[] = generatePhotoData(homeImagePaths, 'residential');
const businessPhotos: ShowcasePhoto[] = generatePhotoData(businessImagePaths, 'business');

// Combined for backward compatibility
const showcasePhotos: ShowcasePhoto[] = [...homePhotos, ...businessPhotos];
---

<BaseLayout title={title} description={description}>
  <!-- Navigation Header -->
  <Navigation slot="header" currentPath="/" />

  <!-- Packages Data for Alpine.js -->
  <script id="packages-data" type="application/json" set:html={JSON.stringify(packages)}></script>

  <!-- Hero Section -->
  <section id="home" class="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-primary-900 via-primary-800 to-secondary-800">
    <!-- Background Image -->
    <div class="absolute inset-0 bg-black/40"></div>
    <img
      src="/images/MPP Hero.webp"
      alt="Mountain Porch Pumpkins professional autumn display - Premium pumpkin delivery service in Utah, Idaho, and Wyoming"
      class="absolute inset-0 w-full h-full object-cover"
      style="filter: brightness(0.8);"
      loading="eager"
      decoding="async"
    >

    <!-- Hero Content -->
    <div class="relative z-10 text-center text-white px-4 max-w-4xl mx-auto">
      <!-- Enhanced background for better text visibility -->
      <div class="bg-black/50 backdrop-blur-sm rounded-3xl p-8 md:p-12 border border-white/10">
        <h1 class="text-4xl md:text-6xl font-serif font-bold mb-6 hero-text-shadow">
          <span class="block">Professional Pumpkin Delivery</span>
          <span class="block text-primary-200 text-2xl md:text-4xl mt-2">Utah • Idaho • Wyoming</span>
        </h1>

        <p class="text-xl md:text-2xl mb-8 hero-text-shadow max-w-2xl mx-auto font-medium">
          Bring the magic of the fall season home while saving time, and your back
        </p>

        <div class="bg-black/40 backdrop-blur-sm rounded-xl p-4 mb-8 max-w-lg mx-auto border border-primary-400/30">
          <p class="text-primary-200 font-semibold text-lg">
            🍂 Setup begins September 21st
          </p>
          <p class="text-white text-sm">
            Book now for fall 2025 delivery across Utah, Idaho & Wyoming
          </p>
        </div>
      </div>

      <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
        <button
          @click="$magic.scrollTo('contact')"
          class="bg-primary-500 hover:bg-primary-600 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all transform hover:scale-105 tap-target focus:ring-2 focus:ring-primary-300 focus:ring-offset-2"
        >
          Get Free Quote
        </button>
        <p class="text-primary-200 font-medium">100% Satisfaction Guaranteed</p>
      </div>
    </div>
  </section>

  <!-- Quick Stats Section -->
  <section class="py-16 bg-white">
    <div class="container mx-auto px-4">
      <div class="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
        <div class="space-y-2">
          <div class="text-3xl md:text-4xl font-bold text-primary-600">500+</div>
          <div class="text-gray-600">Happy Customers</div>
        </div>
        <div class="space-y-2">
          <div class="text-3xl md:text-4xl font-bold text-primary-600">3</div>
          <div class="text-gray-600">States Served</div>
        </div>
        <div class="space-y-2">
          <div class="text-3xl md:text-4xl font-bold text-primary-600">48hr</div>
          <div class="text-gray-600">Setup Time</div>
        </div>
        <div class="space-y-2">
          <div class="text-3xl md:text-4xl font-bold text-primary-600">100%</div>
          <div class="text-gray-600">Satisfaction</div>
        </div>
      </div>
    </div>
  </section>

  <!-- Photo Showcase Section -->
  <section id="showcase" class="py-20 bg-gray-900">
    <div class="container mx-auto px-4">
      <div class="text-center mb-16">
        <h2 class="text-3xl md:text-5xl font-serif font-bold text-white mb-4">
          Design Inspiration
        </h2>
        <p class="text-xl text-gray-300 max-w-2xl mx-auto mb-8">
          Get inspired by beautiful pumpkin displays from our customers across Utah, Idaho, and Wyoming.
        </p>

      </div>

      <!-- Photo Carousel with Toggle System -->
      <div
        class="max-w-6xl mx-auto"
        x-data="{ activeFilter: 'home' }"
      >
        <!-- Toggle Buttons -->
        <div class="flex justify-center mb-8">
          <div class="bg-gray-800 rounded-xl p-1 flex">
            <button
              @click="activeFilter = 'home'"
              :class="activeFilter === 'home' ? 'bg-primary-500 text-white' : 'text-gray-300 hover:text-white'"
              class="px-6 py-3 rounded-lg font-semibold transition-all duration-300 flex items-center space-x-2"
            >
              <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
              </svg>
              <span>Home Porches</span>
            </button>
            <button
              @click="activeFilter = 'business'"
              :class="activeFilter === 'business' ? 'bg-primary-500 text-white' : 'text-gray-300 hover:text-white'"
              class="px-6 py-3 rounded-lg font-semibold transition-all duration-300 flex items-center space-x-2"
            >
              <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
              </svg>
              <span>Business & Events</span>
            </button>
          </div>
        </div>

        <!-- Dynamic Photo Carousel -->
        <!-- Home Photos Carousel -->
        <div x-show="activeFilter === 'home'" x-transition:enter="transition ease-out duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" x-transition:leave="transition ease-in duration-200" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0">
          <PhotoCarousel photos={homePhotos} class="w-full" id="home-carousel" />
        </div>

        <!-- Business Photos Carousel -->
        <div x-show="activeFilter === 'business'" x-transition:enter="transition ease-out duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" x-transition:leave="transition ease-in duration-200" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0" style="display: none;">
          <PhotoCarousel photos={businessPhotos} class="w-full" id="business-carousel" />
        </div>
      </div>
    </div>
  </section>

  <!-- Hidden data for Alpine.js -->
  <script id="home-photos-data" type="application/json" set:html={JSON.stringify(homePhotos)}></script>
  <script id="business-photos-data" type="application/json" set:html={JSON.stringify(businessPhotos)}></script>
  <script id="showcase-photos-data" type="application/json" set:html={JSON.stringify(showcasePhotos)}></script>

  <!-- Packages Section -->
  <section id="packages" class="py-20 bg-gray-50 lazy-content" data-component="packages">
    <div class="container mx-auto px-4">
      <div class="text-center mb-16">
        <h2 class="text-3xl md:text-5xl font-serif font-bold text-gray-900 mb-4">
          Choose Your Perfect Package
        </h2>
        <p class="text-xl text-gray-600 max-w-2xl mx-auto">
          From cozy apartment porches to grand estate displays, we have the perfect pumpkin package for every space. Proudly serving Utah, Southern Idaho, and Western Wyoming.
        </p>
        <div class="mt-6 flex flex-wrap justify-center gap-4 text-sm text-gray-500">
          <span class="flex items-center">
            <svg class="w-4 h-4 mr-1 text-primary-500" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
            </svg>
            Salt Lake City • Park City • Jackson Hole
          </span>
          <span class="flex items-center">
            <svg class="w-4 h-4 mr-1 text-primary-500" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
            </svg>
            Sun Valley • Boise • Moab • St. George • Cache Valley
          </span>
        </div>
      </div>

      <!-- Residential Packages -->
      <div class="mb-16">
        <h3 class="text-2xl font-serif font-bold text-gray-900 mb-8 text-center">Residential Packages</h3>
        <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-7xl mx-auto">
          {packages.filter(pkg => pkg.category === 'residential').map((pkg, index) => (
            <PackageCard package={pkg} featured={index === 2} />
          ))}
        </div>
      </div>

      <!-- Business & Event Packages -->
      <div>
        <h3 class="text-2xl font-serif font-bold text-gray-900 mb-8 text-center">Business & Event Solutions</h3>
        <div class="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
          {packages.filter(pkg => pkg.category !== 'residential').map(pkg => (
            <PackageCard package={pkg} />
          ))}
        </div>
      </div>
    </div>
  </section>

  <!-- Universal Features Section -->
  <UniversalFeatures />

  <!-- Service Areas Section -->
  <section class="py-20 bg-primary-50 lazy-content" data-component="service-areas">
    <div class="container mx-auto px-4">
      <div class="text-center mb-16">
        <h2 class="text-3xl md:text-5xl font-serif font-bold text-gray-900 mb-4">
          We Serve the Mountain West
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
          Professional pumpkin delivery and setup across Utah, Southern Idaho, and Western Wyoming. Setup begins September 21st for residential customers.
        </p>
      </div>

      <div class="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
        <!-- Utah -->
        <div class="bg-white rounded-xl p-8 shadow-lg">
          <h3 class="text-2xl font-bold text-gray-900 mb-4 flex items-center">
            <span class="text-3xl mr-3">🏔️</span>
            Utah
          </h3>
          <ul class="space-y-2 text-gray-600">
            <li>• Salt Lake City (Wasatch Front)</li>
            <li>• Park City</li>
            <li>• Ogden</li>
            <li>• Provo</li>
            <li>• St. George</li>
            <li>• Cache Valley (Logan)</li>
            <li>• Moab</li>
          </ul>
        </div>

        <!-- Idaho -->
        <div class="bg-white rounded-xl p-8 shadow-lg">
          <h3 class="text-2xl font-bold text-gray-900 mb-4 flex items-center">
            <span class="text-3xl mr-3">🌲</span>
            Southern Idaho
          </h3>
          <ul class="space-y-2 text-gray-600">
            <li>• Boise (Treasure Valley)</li>
            <li>• Sun Valley</li>
            <li>• Rexburg</li>
            <li>• Idaho Falls</li>
            <li>• Twin Falls</li>
            <li>• Pocatello</li>
          </ul>
        </div>

        <!-- Wyoming -->
        <div class="bg-white rounded-xl p-8 shadow-lg">
          <h3 class="text-2xl font-bold text-gray-900 mb-4 flex items-center">
            <span class="text-3xl mr-3">⛰️</span>
            Western Wyoming
          </h3>
          <ul class="space-y-2 text-gray-600">
            <li>• Jackson Hole</li>
            <li>• Teton Village</li>
            <li>• Wilson</li>
            <li>• Star Valley</li>
          </ul>
        </div>
      </div>

      <div class="text-center mt-12">
        <p class="text-lg text-gray-600 mb-4">
          <strong>Expanding Soon:</strong> Nevada and Arizona (2025)
        </p>
        <p class="text-sm text-gray-500">
          Don't see your area? Contact us - we may be able to arrange special delivery!
        </p>
      </div>
    </div>
  </section>

  <!-- About Section -->
  <section id="about" class="py-20 bg-white lazy-content" data-component="about">
    <div class="container mx-auto px-4">
      <div class="max-w-4xl mx-auto text-center">
        <h2 class="text-3xl md:text-5xl font-serif font-bold text-gray-900 mb-8">
          Why Choose Mountain Porch Pumpkins?
        </h2>

        <div class="grid md:grid-cols-3 gap-8 mb-12">
          <div class="text-center">
            <div class="bg-primary-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
              <svg class="h-8 w-8 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">Save Time</h3>
            <p class="text-gray-600">Skip the pumpkin patch hassle. We handle everything from selection to setup, giving you more time with family.</p>
          </div>

          <div class="text-center">
            <div class="bg-primary-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
              <svg class="h-8 w-8 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">Professional Quality</h3>
            <p class="text-gray-600">Hand-selected premium pumpkins and expert arrangement for picture-perfect displays that last all season.</p>
          </div>

          <div class="text-center">
            <div class="bg-primary-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
              <svg class="h-8 w-8 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">100% Satisfaction</h3>
            <p class="text-gray-600">We guarantee you'll love your display or we'll make it right.</p>
          </div>
        </div>

        <div class="bg-gray-50 rounded-2xl p-8">
          <h3 class="text-2xl font-serif font-bold text-gray-900 mb-4">Our Story</h3>
          <p class="text-gray-600 text-lg leading-relaxed mb-4">
            Founded in Riverdale, Utah, Mountain Porch Pumpkins was born from a simple idea: families should spend more time enjoying fall traditions and less time driving to pumpkin patches. We understand that busy mountain families want beautiful autumn displays without the hassle of farm visits, heavy lifting, and time-consuming setup.
          </p>
          <p class="text-gray-600 text-lg leading-relaxed mb-4">
            What started as a local service has grown into the premier pumpkin delivery company across Utah, Idaho, and Wyoming. We've helped thousands of families create picture-perfect fall displays, from cozy apartment porches to grand mountain estates.
          </p>
          <p class="text-gray-600 text-lg leading-relaxed">
            Our team hand-selects premium pumpkins and creates professional arrangements that bring the magic of autumn directly to your doorstep. With setup beginning September 21st each year, we're here to make your fall season more beautiful and less stressful.
          </p>

          <div class="mt-6 grid md:grid-cols-3 gap-4 text-center">
            <div class="bg-white rounded-lg p-4">
              <div class="text-2xl font-bold text-primary-600">2020</div>
              <div class="text-sm text-gray-600">Founded</div>
            </div>
            <div class="bg-white rounded-lg p-4">
              <div class="text-2xl font-bold text-primary-600">3</div>
              <div class="text-sm text-gray-600">States Served</div>
            </div>
            <div class="bg-white rounded-lg p-4">
              <div class="text-2xl font-bold text-primary-600">1000+</div>
              <div class="text-sm text-gray-600">Happy Customers</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Contact Section -->
  <section id="contact" class="py-20 bg-gradient-to-br from-primary-50 to-secondary-50 lazy-content" data-component="contact">
    <div class="container mx-auto px-4">
      <div class="max-w-4xl mx-auto">
        <div class="lazy-component" data-component="form">
          <ContactForm />
        </div>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer slot="footer" class="bg-gray-900 text-white py-12">
    <div class="container mx-auto px-4">
      <div class="grid md:grid-cols-4 gap-8 mb-8">
        <!-- Brand -->
        <div class="md:col-span-2">
          <div class="flex items-center space-x-3 mb-4">
            <img src="/favicon.svg" alt="Mountain Porch Pumpkins Logo" class="h-8 w-8">
            <span class="font-serif font-bold text-xl">Mountain Porch Pumpkins</span>
          </div>
          <p class="text-gray-400 mb-4 max-w-md">
            Professional pumpkin delivery and display service bringing autumn magic to your doorstep across Utah, Idaho, and Wyoming.
          </p>
          <div class="flex space-x-4">
            <!-- Instagram -->
            <a href="#" class="text-gray-400 hover:text-white transition-colors" aria-label="Follow us on Instagram">
              <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
              </svg>
            </a>
            <!-- TikTok -->
            <a href="#" class="text-gray-400 hover:text-white transition-colors" aria-label="Follow us on TikTok">
              <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"/>
              </svg>
            </a>
            <!-- Pinterest -->
            <a href="#" class="text-gray-400 hover:text-white transition-colors" aria-label="Follow us on Pinterest">
              <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.746-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001.012.001z"/>
              </svg>
            </a>
            <!-- Facebook -->
            <a href="#" class="text-gray-400 hover:text-white transition-colors" aria-label="Follow us on Facebook">
              <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
              </svg>
            </a>
          </div>
        </div>

        <!-- Quick Links -->
        <div>
          <h4 class="font-semibold text-white mb-4">Quick Links</h4>
          <ul class="space-y-2">
            <li><a href="#packages" class="text-gray-400 hover:text-white transition-colors">Packages</a></li>
            <li><a href="#about" class="text-gray-400 hover:text-white transition-colors">About Us</a></li>
            <li><a href="#contact" class="text-gray-400 hover:text-white transition-colors">Get Quote</a></li>
            <li><a href="/business" class="text-gray-400 hover:text-white transition-colors">Business Services</a></li>
          </ul>
        </div>

        <!-- Contact Info -->
        <div>
          <h4 class="font-semibold text-white mb-4">Contact</h4>
          <ul class="space-y-2 text-gray-400">
            <li class="flex items-center">
              <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
              </svg>
              (801) 555-0123
            </li>
            <li class="flex items-center">
              <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
              <EMAIL>
            </li>
            <li class="flex items-start">
              <svg class="h-4 w-4 mr-2 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              <span>Serving Utah, Idaho & Wyoming</span>
            </li>
          </ul>
        </div>
      </div>

      <!-- Bottom Bar -->
      <div class="border-t border-gray-800 pt-8 text-center text-gray-400">
        <p>&copy; 2024 Mountain Porch Pumpkins. All rights reserved.</p>
        <div class="mt-2 space-x-4">
          <a href="/privacy" class="hover:text-white transition-colors">Privacy Policy</a>
          <a href="/terms" class="hover:text-white transition-colors">Terms of Service</a>
        </div>
      </div>
    </div>
  </footer>
</BaseLayout>

<style>
  /* Enhanced text shadow for hero section */
  .hero-text-shadow {
    text-shadow:
      3px 3px 6px rgba(0, 0, 0, 0.9),
      -2px -2px 4px rgba(0, 0, 0, 0.7),
      2px -2px 4px rgba(0, 0, 0, 0.7),
      -2px 2px 4px rgba(0, 0, 0, 0.7),
      0px 0px 12px rgba(0, 0, 0, 0.8),
      0px 0px 20px rgba(0, 0, 0, 0.6);
  }
</style>
