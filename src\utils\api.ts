// Shared API utilities for Mountain Porch Pumpkins
// Standardizes API response patterns and eliminates duplication

import type { StandardApiResponse } from '../types';

/**
 * Creates a standardized success response
 */
export function createSuccessResponse<T>(
  data?: T, 
  message?: string
): Response {
  const response: StandardApiResponse<T> = {
    success: true,
    data,
    message
  };

  return new Response(JSON.stringify(response), {
    status: 200,
    headers: {
      'Content-Type': 'application/json',
      'Cache-Control': 'no-cache'
    }
  });
}

/**
 * Creates a standardized error response
 */
export function createErrorResponse(
  error: string,
  status: number = 400,
  errors?: Record<string, string>
): Response {
  const response: StandardApiResponse = {
    success: false,
    error,
    errors
  };

  return new Response(JSON.stringify(response), {
    status,
    headers: {
      'Content-Type': 'application/json',
      'Cache-Control': 'no-cache'
    }
  });
}

/**
 * Creates a validation error response
 */
export function createValidationErrorResponse(
  errors: Record<string, string>
): Response {
  return createErrorResponse(
    'Validation failed',
    400,
    errors
  );
}

/**
 * Creates a server error response
 */
export function createServerErrorResponse(
  message: string = 'Internal server error'
): Response {
  return createErrorResponse(message, 500);
}

/**
 * Creates a not found response
 */
export function createNotFoundResponse(
  message: string = 'Resource not found'
): Response {
  return createErrorResponse(message, 404);
}

/**
 * Creates an unauthorized response
 */
export function createUnauthorizedResponse(
  message: string = 'Unauthorized'
): Response {
  return createErrorResponse(message, 401);
}

/**
 * Safely parses JSON from request body
 */
export async function safeParseJson(request: Request): Promise<{ success: true; data: any } | { success: false; error: string }> {
  try {
    const data = await request.json();
    return { success: true, data };
  } catch (error) {
    return { 
      success: false, 
      error: 'Invalid JSON in request body' 
    };
  }
}

/**
 * Extracts client metadata from request
 */
export function extractClientMetadata(request: Request): {
  ip: string;
  userAgent: string;
  timestamp: number;
} {
  return {
    ip: request.headers.get('x-forwarded-for') || 
        request.headers.get('x-real-ip') || 
        'unknown',
    userAgent: request.headers.get('user-agent') || 'unknown',
    timestamp: Date.now()
  };
}

/**
 * Validates required fields in request data
 */
export function validateRequiredFields(
  data: Record<string, any>, 
  requiredFields: string[]
): { isValid: true } | { isValid: false; missingFields: string[] } {
  const missingFields = requiredFields.filter(field => 
    !data[field] || (typeof data[field] === 'string' && !data[field].trim())
  );

  if (missingFields.length > 0) {
    return { isValid: false, missingFields };
  }

  return { isValid: true };
}

/**
 * Common CORS headers for API responses
 */
export const CORS_HEADERS = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Max-Age': '86400'
};

/**
 * Creates an OPTIONS response for CORS preflight
 */
export function createOptionsResponse(): Response {
  return new Response(null, {
    status: 200,
    headers: CORS_HEADERS
  });
}

/**
 * Adds CORS headers to an existing response
 */
export function addCorsHeaders(response: Response): Response {
  Object.entries(CORS_HEADERS).forEach(([key, value]) => {
    response.headers.set(key, value);
  });
  return response;
}

/**
 * Rate limiting helper (simple in-memory implementation)
 */
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

export function checkRateLimit(
  identifier: string, 
  maxRequests: number = 10, 
  windowMs: number = 60000
): { allowed: true } | { allowed: false; resetTime: number } {
  const now = Date.now();
  const key = identifier;
  
  const current = rateLimitStore.get(key);
  
  if (!current || now > current.resetTime) {
    rateLimitStore.set(key, { count: 1, resetTime: now + windowMs });
    return { allowed: true };
  }
  
  if (current.count >= maxRequests) {
    return { allowed: false, resetTime: current.resetTime };
  }
  
  current.count++;
  return { allowed: true };
}

/**
 * Creates a rate limit exceeded response
 */
export function createRateLimitResponse(resetTime: number): Response {
  return new Response(JSON.stringify({
    success: false,
    error: 'Rate limit exceeded',
    message: 'Too many requests. Please try again later.',
    resetTime
  }), {
    status: 429,
    headers: {
      'Content-Type': 'application/json',
      'Retry-After': Math.ceil((resetTime - Date.now()) / 1000).toString()
    }
  });
}

/**
 * Logs API request for debugging (development only)
 */
export function logApiRequest(
  method: string, 
  url: string, 
  data?: any, 
  metadata?: any
): void {
  if (process.env.NODE_ENV === 'development') {
    console.log(`🔗 API ${method} ${url}`, {
      data: data ? JSON.stringify(data, null, 2) : 'No data',
      metadata
    });
  }
}
