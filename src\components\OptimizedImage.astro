---
// Optimized Image component following SOLID principles
// Single Responsibility: Handle image optimization and lazy loading
// Open/Closed: Extensible through props
// Interface Segregation: Clean image interface

import { generateResponsiveImageData, type ImageOptimizationConfig } from '../utils/image-optimization';

export interface Props {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  sizes?: string;
  loading?: 'lazy' | 'eager';
  decoding?: 'async' | 'sync' | 'auto';
  class?: string;
  quality?: number;
  format?: 'webp' | 'avif' | 'jpeg' | 'png';
  placeholder?: 'blur' | 'none';
  priority?: boolean;
}

const {
  src,
  alt,
  width = 800,
  height,
  sizes = '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',
  loading = 'lazy',
  decoding = 'async',
  class: className = '',
  quality = 80,
  format = 'webp',
  placeholder = 'blur',
  priority = false
} = Astro.props;

// Use shared image optimization utilities
const config: ImageOptimizationConfig = {
  width,
  height,
  quality,
  format,
  sizes,
  loading,
  decoding,
  priority,
  placeholder
};

const imageData = generateResponsiveImageData(src, config);
---

<div 
  class:list={[
    'relative overflow-hidden',
    className
  ]}
  style={imageData.aspectRatio ? `aspect-ratio: ${imageData.aspectRatio}` : undefined}
>

  
  <!-- Main optimized image with enhanced lazy loading -->
  {imageData.actualLoading === 'lazy' ? (
    imageData.srcset ? (
      <img
        src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1 1'%3E%3C/svg%3E"
        data-src={imageData.src}
        data-srcset={imageData.srcset}
        sizes={sizes}
        alt={alt}
        width={width}
        height={height}
        loading="lazy"
        decoding={imageData.actualDecoding}
        class="w-full h-full object-cover lazy-image"
      />
    ) : (
      <img
        src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1 1'%3E%3C/svg%3E"
        data-src={imageData.src}
        alt={alt}
        width={width}
        height={height}
        loading="lazy"
        decoding={imageData.actualDecoding}
        class="w-full h-full object-cover lazy-image"
      />
    )
  ) : (
    imageData.srcset ? (
      <img
        src={imageData.src}
        srcset={imageData.srcset}
        sizes={sizes}
        alt={alt}
        width={width}
        height={height}
        loading="eager"
        decoding={imageData.actualDecoding}
        class="w-full h-full object-cover"
      />
    ) : (
      <img
        src={imageData.src}
        alt={alt}
        width={width}
        height={height}
        loading="eager"
        decoding={imageData.actualDecoding}
        class="w-full h-full object-cover"
      />
    )
  )}
  

</div>



<style>
  /* Responsive image container */
  .image-container {
    position: relative;
    overflow: hidden;
  }

  .image-container::before {
    content: '';
    display: block;
    width: 100%;
    height: 0;
    padding-bottom: var(--aspect-ratio, 56.25%); /* 16:9 default */
  }

  .image-container img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  /* Lazy loading styles */
  .lazy-image {
    opacity: 1;
    background-color: #f3f4f6; /* Light gray background while loading */
  }

  .lazy-image.lazy-loaded {
    background-color: transparent;
  }

  /* Ensure all images are visible (no flashing effects) */
  img {
    opacity: 1;
  }
</style>
