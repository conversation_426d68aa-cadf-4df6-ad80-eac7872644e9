---
// Optimized Image component following SOLID principles
// Single Responsibility: Handle image optimization and lazy loading
// Open/Closed: Extensible through props
// Interface Segregation: Clean image interface

export interface Props {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  sizes?: string;
  loading?: 'lazy' | 'eager';
  decoding?: 'async' | 'sync' | 'auto';
  class?: string;
  quality?: number;
  format?: 'webp' | 'avif' | 'jpeg' | 'png';
  placeholder?: 'blur' | 'none';
  priority?: boolean;
}

const {
  src,
  alt,
  width,
  height,
  sizes = '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',
  loading = 'lazy',
  decoding = 'async',
  class: className = '',
  quality = 80,
  format = 'webp',
  placeholder = 'blur',
  priority = false
} = Astro.props;

// Generate responsive image URLs
function generateImageUrl(src: string, width: number, quality: number = 80, format: string = 'webp'): string {
  // For external URLs (like Unsplash), use their optimization parameters
  if (src.startsWith('http')) {
    const url = new URL(src);
    url.searchParams.set('w', width.toString());
    url.searchParams.set('q', quality.toString());
    url.searchParams.set('fm', format);
    url.searchParams.set('fit', 'crop');
    url.searchParams.set('crop', 'center');
    return url.toString();
  }
  
  // For local images, would use Astro's image optimization
  // This is a placeholder for local image handling
  return src;
}

// Generate srcset for responsive images
const widths = [320, 640, 768, 1024, 1280, 1920];
const srcset = src.startsWith('http')
  ? widths.map(w => `${generateImageUrl(src, w, quality, format)} ${w}w`).join(', ')
  : ''; // Don't generate srcset for local images without processing

// Generate placeholder for blur effect
const placeholderSrc = src.startsWith('http')
  ? generateImageUrl(src, 20, 20, 'jpeg')
  : src; // Use original image for local files

// Determine loading strategy
const actualLoading = priority ? 'eager' : loading;
const actualDecoding = priority ? 'sync' : decoding;
---

<div 
  class:list={[
    'relative overflow-hidden',
    className
  ]}
  style={width && height ? `aspect-ratio: ${width}/${height}` : undefined}
>

  
  <!-- Main optimized image with enhanced lazy loading -->
  {actualLoading === 'lazy' ? (
    srcset ? (
      <img
        src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1 1'%3E%3C/svg%3E"
        data-src={generateImageUrl(src, width || 800, quality, format)}
        data-srcset={srcset}
        sizes={sizes}
        alt={alt}
        width={width}
        height={height}
        loading="lazy"
        decoding={actualDecoding}
        class="w-full h-full object-cover lazy-image"
      />
    ) : (
      <img
        src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1 1'%3E%3C/svg%3E"
        data-src={generateImageUrl(src, width || 800, quality, format)}
        alt={alt}
        width={width}
        height={height}
        loading="lazy"
        decoding={actualDecoding}
        class="w-full h-full object-cover lazy-image"
      />
    )
  ) : (
    srcset ? (
      <img
        src={generateImageUrl(src, width || 800, quality, format)}
        srcset={srcset}
        sizes={sizes}
        alt={alt}
        width={width}
        height={height}
        loading="eager"
        decoding={actualDecoding}
        class="w-full h-full object-cover"
      />
    ) : (
      <img
        src={generateImageUrl(src, width || 800, quality, format)}
        alt={alt}
        width={width}
        height={height}
        loading="eager"
        decoding={actualDecoding}
        class="w-full h-full object-cover"
      />
    )
  )}
  

</div>



<style>
  /* Responsive image container */
  .image-container {
    position: relative;
    overflow: hidden;
  }

  .image-container::before {
    content: '';
    display: block;
    width: 100%;
    height: 0;
    padding-bottom: var(--aspect-ratio, 56.25%); /* 16:9 default */
  }

  .image-container img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  /* Lazy loading styles */
  .lazy-image {
    opacity: 1;
    background-color: #f3f4f6; /* Light gray background while loading */
  }

  .lazy-image.lazy-loaded {
    background-color: transparent;
  }

  /* Ensure all images are visible (no flashing effects) */
  img {
    opacity: 1;
  }
</style>
