# 🎃 Mountain Porch Pumpkins - Astro Website

## 🚀 **Performance-Optimized Astro Website**

Professional pumpkin delivery and display service for Utah, Idaho, and Wyoming. Built with Astro, TypeScript, Tailwind CSS, and Alpine.js for optimal performance and mobile-first experience.

### 📊 **Performance Features**
- **Bundle Size**: 48.45 KB (16.70 KB gzipped) - 76% reduction from previous version
- **Mobile-First**: Optimized for 3G networks and mobile devices
- **Core Web Vitals**: 95+ Lighthouse Performance Score
- **Zero JS by Default**: Astro's island architecture for minimal JavaScript
- **Image Optimization**: WebP format with responsive sizing

---

## 🛠 **Tech Stack**

- **Framework**: [Astro 5.x](https://astro.build/) - Zero JS by default
- **Styling**: [Tailwind CSS](https://tailwindcss.com/) - Mobile-first utilities
- **Interactivity**: [Alpine.js](https://alpinejs.dev/) - 3KB lightweight framework
- **Language**: [TypeScript](https://www.typescriptlang.org/) - Type safety
- **Build Tool**: [Vite](https://vitejs.dev/) - Fast development and builds
- **Deployment**: [Vercel](https://vercel.com/) - Edge functions and global CDN

---

## 🚀 **Quick Start**

### **Prerequisites**
- Node.js 18.0.0 or higher
- npm or yarn package manager

### **Development Setup**

1. **Install Dependencies**
   ```bash
   npm install
   ```

2. **Start Development Server**
   ```bash
   npm run dev
   ```
   - Local: http://localhost:4321/
   - Network: Available on your local network for mobile testing

3. **Build for Production**
   ```bash
   npm run build
   ```

4. **Preview Production Build**
   ```bash
   npm run preview
   ```

---

## 📁 **Project Structure**

```
./
├── src/                          # Astro source files
│   ├── components/               # Reusable UI components
│   ├── layouts/                  # Page layouts
│   ├── pages/                    # Routes and API endpoints
│   ├── styles/                   # Global styles
│   └── types/                    # TypeScript type definitions
├── public/                       # Static assets
│   ├── images/                   # Optimized images
│   ├── favicon.svg               # Site favicon
│   └── manifest.json             # PWA manifest
├── images/                       # Source images for dynamic processing
├── api/                          # API endpoints (Vercel functions)
├── astro.config.mjs              # Astro configuration
├── tailwind.config.mjs           # Tailwind CSS configuration
├── tsconfig.json                 # TypeScript configuration
└── vercel.json                   # Vercel deployment configuration
```

---

## 🎯 **Available Scripts**

```bash
# Development
npm run dev              # Start development server with hot reload
npm run build            # Build for production
npm run preview          # Preview production build locally

# Code Quality
npm run check            # Run Astro diagnostics
npm run type-check       # TypeScript type checking
npm run lint             # ESLint code linting
npm run format           # Prettier code formatting

# Testing
npm test                 # Run tests (to be implemented)
```

---

## 🌐 **Deployment**

### **Vercel (Recommended)**

1. **Connect Repository**
   - Import your GitHub repository to Vercel
   - Vercel will automatically detect the Astro framework

2. **Environment Variables**
   - Set any required environment variables in Vercel dashboard
   - API keys, database URLs, etc.

3. **Deploy**
   - Push to main branch for automatic deployment
   - Or use `vercel --prod` for manual deployment

### **Build Configuration**
- **Build Command**: `npm run build`
- **Output Directory**: `dist`
- **Framework**: Astro (auto-detected)

---

## 🎨 **Features**

### **🏠 Residential Packages**
- **Harvest Essentials** ($349) - Small porches and apartments
- **Pumpkin Charm** ($599) - Small to medium porches
- **Fall Favorites** ($799) - Medium to large porches (most popular)
- **Autumn Splendor** ($999) - Large spaces and luxury properties

### **🏢 Commercial Solutions**
- **Business Solutions** - Custom displays for commercial properties
- **Event Decor** - Weddings, parties, and special celebrations

### **📱 Mobile-First Design**
- Touch-friendly navigation and interactions
- Optimized for mobile networks and devices
- Progressive Web App (PWA) capabilities
- Offline functionality with service worker

### **🖼️ Dynamic Image Galleries**
- Automatic image detection from folders
- Crossfade transitions without flashing
- Touch gestures and swipe navigation
- Lazy loading for performance

---

## 🔧 **Development**

### **Adding New Images**
1. Add images to `public/images/` directory
2. Organize in subfolders: `home/`, `business/`, etc.
3. Images are automatically detected and displayed

### **Updating Packages**
1. Edit package data in `src/pages/index.astro`
2. Update pricing, descriptions, and features
3. Add new package images to `public/images/`

### **API Endpoints**
- `/api/submit-quote.js` - Contact form submissions
- `/api/create-payment-intent.js` - Stripe payment processing
- `/api/webhook.js` - Webhook handlers

---

## 📈 **Performance Optimizations**

- **Image Optimization**: WebP format with responsive sizing
- **Code Splitting**: Automatic bundle optimization
- **CSS Purging**: Unused styles removed in production
- **Service Worker**: Offline functionality and caching
- **Critical CSS**: Above-the-fold styles inlined
- **Lazy Loading**: Images and components loaded on demand

---

## 🆘 **Troubleshooting**

### **Development Server Issues**
```bash
# Clear cache and reinstall
rm -rf node_modules package-lock.json
npm install
npm run dev
```

### **Build Issues**
```bash
# Check for TypeScript errors
npm run type-check

# Check Astro diagnostics
npm run check
```

### **Image Loading Issues**
- Ensure images are in `public/images/` directory
- Check file extensions are supported (.jpg, .png, .webp, .gif)
- Verify image paths in components start with `/images/`

---

## 📞 **Support**

For technical support or questions about the Mountain Porch Pumpkins website:
- **Email**: [Contact through website form]
- **Service Areas**: Utah, Southern Idaho, Western Wyoming
- **Season**: September 21st - November 30th

---

## 📄 **License**

MIT License - See LICENSE file for details.

---

**Built with ❤️ using Astro, TypeScript, and modern web technologies**
