// Shared validation utilities for Mountain Porch Pumpkins
// Eliminates code duplication across API endpoints and components

import type { ValidationRule, FieldValidation, FormValidationResult } from '../types';

/**
 * Validates an email address
 */
export function validateEmail(email: string): FieldValidation {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  const isValid = emailRegex.test(email.trim());
  
  return {
    isValid,
    error: isValid ? undefined : 'Please enter a valid email address'
  };
}

/**
 * Validates a phone number
 */
export function validatePhone(phone: string): FieldValidation {
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
  const cleanPhone = phone.replace(/\D/g, '');
  const isValid = phoneRegex.test(cleanPhone) && cleanPhone.length >= 10;
  
  return {
    isValid,
    error: isValid ? undefined : 'Please enter a valid phone number (10+ digits)'
  };
}

/**
 * Validates a required field
 */
export function validateRequired(value: string, fieldName: string = 'This field'): FieldValidation {
  const isValid = value && value.trim().length > 0;
  
  return {
    isValid,
    error: isValid ? undefined : `${fieldName} is required`
  };
}

/**
 * Validates minimum length
 */
export function validateMinLength(value: string, minLength: number, fieldName: string = 'This field'): FieldValidation {
  const isValid = value.trim().length >= minLength;
  
  return {
    isValid,
    error: isValid ? undefined : `${fieldName} must be at least ${minLength} characters`
  };
}

/**
 * Validates maximum length
 */
export function validateMaxLength(value: string, maxLength: number, fieldName: string = 'This field'): FieldValidation {
  const isValid = value.trim().length <= maxLength;
  
  return {
    isValid,
    error: isValid ? undefined : `${fieldName} must be no more than ${maxLength} characters`
  };
}

/**
 * Validates a field against a pattern
 */
export function validatePattern(value: string, pattern: RegExp, message: string): FieldValidation {
  const isValid = pattern.test(value);
  
  return {
    isValid,
    error: isValid ? undefined : message
  };
}

/**
 * Validates a single field against multiple rules
 */
export function validateField(value: string, rules: ValidationRule[], fieldName: string = 'Field'): FieldValidation {
  for (const rule of rules) {
    let result: FieldValidation;
    
    switch (rule.type) {
      case 'required':
        result = validateRequired(value, fieldName);
        break;
      case 'email':
        result = validateEmail(value);
        break;
      case 'phone':
        result = validatePhone(value);
        break;
      case 'minLength':
        result = validateMinLength(value, rule.value as number, fieldName);
        break;
      case 'maxLength':
        result = validateMaxLength(value, rule.value as number, fieldName);
        break;
      case 'pattern':
        result = validatePattern(value, new RegExp(rule.value as string), rule.message);
        break;
      default:
        continue;
    }
    
    if (!result.isValid) {
      return result;
    }
  }
  
  return { isValid: true };
}

/**
 * Validates an entire form object
 */
export function validateForm(
  formData: Record<string, string>, 
  validationRules: Record<string, ValidationRule[]>
): FormValidationResult {
  const errors: Record<string, string> = {};
  
  for (const [fieldName, rules] of Object.entries(validationRules)) {
    const value = formData[fieldName] || '';
    const result = validateField(value, rules, fieldName);
    
    if (!result.isValid && result.error) {
      errors[fieldName] = result.error;
    }
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
}

/**
 * Common validation rules for contact forms
 */
export const CONTACT_FORM_RULES: Record<string, ValidationRule[]> = {
  firstName: [
    { type: 'required', message: 'First name is required' },
    { type: 'minLength', value: 2, message: 'First name must be at least 2 characters' }
  ],
  lastName: [
    { type: 'required', message: 'Last name is required' },
    { type: 'minLength', value: 2, message: 'Last name must be at least 2 characters' }
  ],
  email: [
    { type: 'required', message: 'Email is required' },
    { type: 'email', message: 'Please enter a valid email address' }
  ],
  phone: [
    { type: 'required', message: 'Phone number is required' },
    { type: 'phone', message: 'Please enter a valid phone number' }
  ],
  address: [
    { type: 'required', message: 'Address is required' },
    { type: 'minLength', value: 10, message: 'Please enter a complete address' }
  ],
  city: [
    { type: 'required', message: 'City is required' },
    { type: 'minLength', value: 2, message: 'City must be at least 2 characters' }
  ],
  state: [
    { type: 'required', message: 'State is required' }
  ],
  zipCode: [
    { type: 'required', message: 'ZIP code is required' },
    { type: 'pattern', value: '^[0-9]{5}(-[0-9]{4})?$', message: 'Please enter a valid ZIP code' }
  ],
  package: [
    { type: 'required', message: 'Please select a package' }
  ]
};

/**
 * Sanitizes user input to prevent XSS
 */
export function sanitizeInput(input: string): string {
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .substring(0, 1000); // Limit length
}

/**
 * Validates and sanitizes form data
 */
export function validateAndSanitizeForm(
  formData: Record<string, string>,
  validationRules: Record<string, ValidationRule[]>
): { isValid: boolean; errors: Record<string, string>; sanitizedData: Record<string, string> } {
  // Sanitize all inputs first
  const sanitizedData: Record<string, string> = {};
  for (const [key, value] of Object.entries(formData)) {
    sanitizedData[key] = sanitizeInput(value);
  }

  // Validate sanitized data
  const validation = validateForm(sanitizedData, validationRules);

  return {
    isValid: validation.isValid,
    errors: validation.errors,
    sanitizedData
  };
}

/**
 * Common form field configurations to eliminate duplication
 */
export const FORM_FIELD_CONFIGS = {
  firstName: {
    type: 'text',
    autocomplete: 'given-name',
    placeholder: 'Enter your first name',
    required: true,
    validation: ['required']
  },
  lastName: {
    type: 'text',
    autocomplete: 'family-name',
    placeholder: 'Enter your last name',
    required: true,
    validation: ['required']
  },
  email: {
    type: 'email',
    autocomplete: 'email',
    placeholder: '<EMAIL>',
    required: true,
    validation: ['required', 'email']
  },
  phone: {
    type: 'tel',
    autocomplete: 'tel',
    placeholder: '(*************',
    required: true,
    validation: ['required', 'phone']
  },
  streetAddress: {
    type: 'text',
    autocomplete: 'street-address',
    placeholder: '123 Main Street',
    required: true,
    validation: ['required']
  },
  city: {
    type: 'text',
    autocomplete: 'address-level2',
    placeholder: 'Salt Lake City',
    required: true,
    validation: ['required']
  },
  state: {
    type: 'select',
    autocomplete: 'address-level1',
    required: true,
    validation: ['required']
  },
  zipCode: {
    type: 'text',
    autocomplete: 'postal-code',
    placeholder: '84101',
    maxlength: 5,
    required: true,
    validation: ['required']
  },
  message: {
    type: 'textarea',
    rows: 4,
    placeholder: 'Tell us about your space, any special requirements, or questions you have...',
    required: false,
    validation: []
  }
} as const;
