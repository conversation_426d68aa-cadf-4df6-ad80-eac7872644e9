// Package store - Single Responsibility Principle
// Handles package selection and modal state

import type { Package } from '../types';

export interface PackageStore {
  selectedPackage: Package | null;
  packageModalOpen: boolean;
  selectPackage(pkg: Package): void;
  closePackageModal(): void;
  selectPackageInForm(packageId: string): void;
}

export function createPackageStore(): PackageStore {
  return {
    selectedPackage: null,
    packageModalOpen: false,

    selectPackage(pkg: Package) {
      this.selectedPackage = pkg;
      this.packageModalOpen = true;
    },

    closePackageModal() {
      this.packageModalOpen = false;
      this.selectedPackage = null;
    },

    // Global package selection method for form integration
    selectPackageInForm(packageId: string) {
      // Update the form data directly
      const formElement = document.querySelector('[x-data*="contactForm"]');
      if (formElement && (formElement as any)._x_dataStack) {
        const formData = (formElement as any)._x_dataStack[0];
        if (formData && formData.formData) {
          formData.formData.package = packageId;
        }
      }

      // Also update the DOM select element
      const packageSelect = document.getElementById('package') as HTMLSelectElement;
      if (packageSelect) {
        packageSelect.value = packageId;

        // Trigger change event
        const changeEvent = new Event('change', { bubbles: true });
        packageSelect.dispatchEvent(changeEvent);
      }

      // Scroll to contact form
      const contactSection = document.getElementById('contact');
      if (contactSection) {
        contactSection.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }

      // Close package modal if open
      this.closePackageModal();
    }
  };
}
