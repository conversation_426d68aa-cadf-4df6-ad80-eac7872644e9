name: Build and Deploy Optimized Site

on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]
  schedule:
    # Run weekly to check for dependency updates
    - cron: '0 0 * * 0'

jobs:
  build-and-test:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      
    - name: Setup Ruby
      uses: ruby/setup-ruby@v1
      with:
        ruby-version: '3.1'
        bundler-cache: true
        
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: |
        npm ci
        bundle install
        
    - name: Optimize images
      run: |
        node optimize-images.js
        
    - name: Build Jekyll site
      run: |
        JEKYLL_ENV=production bundle exec jekyll build
        
    - name: Copy service worker
      run: |
        cp sw.js _site/ || echo "Service worker not found"
        
    - name: Run performance audit
      run: |
        node performance-audit.js
        
    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: site-build
        path: _site/
        
    - name: Upload performance report
      uses: actions/upload-artifact@v3
      with:
        name: performance-report
        path: performance-audit-report.json
      if: always()

  lighthouse-audit:
    runs-on: ubuntu-latest
    needs: build-and-test
    if: github.event_name == 'pull_request'
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      
    - name: Download build artifacts
      uses: actions/download-artifact@v3
      with:
        name: site-build
        path: _site/
        
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        
    - name: Install Lighthouse CI
      run: npm install -g @lhci/cli@0.12.x
      
    - name: Serve site and run Lighthouse
      run: |
        npx http-server _site -p 8080 &
        sleep 5
        lhci autorun --upload.target=temporary-public-storage --collect.url=http://localhost:8080
        
    - name: Comment PR with Lighthouse results
      uses: actions/github-script@v6
      if: github.event_name == 'pull_request'
      with:
        script: |
          const fs = require('fs');
          try {
            const results = JSON.parse(fs.readFileSync('.lighthouseci/lhr-*.json', 'utf8'));
            const scores = results.categories;
            
            const comment = `## 🚦 Lighthouse Performance Report
            
            | Category | Score |
            |----------|-------|
            | Performance | ${Math.round(scores.performance.score * 100)}/100 |
            | Accessibility | ${Math.round(scores.accessibility.score * 100)}/100 |
            | Best Practices | ${Math.round(scores['best-practices'].score * 100)}/100 |
            | SEO | ${Math.round(scores.seo.score * 100)}/100 |
            
            [View full report](${process.env.LHCI_BUILD_URL})`;
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });
          } catch (error) {
            console.log('Could not post Lighthouse results:', error);
          }

  deploy:
    runs-on: ubuntu-latest
    needs: build-and-test
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/master'
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      
    - name: Download build artifacts
      uses: actions/download-artifact@v3
      with:
        name: site-build
        path: _site/
        
    - name: Deploy to GitHub Pages
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./_site
        cname: mountainporchpumpkins.com  # Replace with your domain
        
  security-scan:
    runs-on: ubuntu-latest
    needs: build-and-test
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
        
    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'

  performance-monitoring:
    runs-on: ubuntu-latest
    needs: deploy
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/master'
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        
    - name: Monitor Core Web Vitals
      run: |
        npx web-vitals-cli https://mountainporchpumpkins.com --output=json > web-vitals.json
        
    - name: Upload Web Vitals report
      uses: actions/upload-artifact@v3
      with:
        name: web-vitals-report
        path: web-vitals.json
        
    - name: Check performance thresholds
      run: |
        node -e "
        const vitals = require('./web-vitals.json');
        const thresholds = { LCP: 2500, FID: 100, CLS: 0.1 };
        let failed = false;
        
        Object.entries(thresholds).forEach(([metric, threshold]) => {
          const value = vitals[metric];
          if (value > threshold) {
            console.log(\`❌ \${metric}: \${value} exceeds threshold \${threshold}\`);
            failed = true;
          } else {
            console.log(\`✅ \${metric}: \${value} within threshold \${threshold}\`);
          }
        });
        
        if (failed) process.exit(1);
        "
