---
// Performance Monitor component
// Tracks Core Web Vitals and performance metrics
// Only loads in production or when explicitly enabled

export interface Props {
  enabled?: boolean;
  debug?: boolean;
  reportEndpoint?: string;
}

const {
  enabled = import.meta.env.PROD,
  debug = import.meta.env.DEV,
  reportEndpoint = '/api/performance'
} = Astro.props;
---

{enabled && (
  <script define:vars={{ debug, reportEndpoint }}>
    // Performance monitoring for Core Web Vitals
    class PerformanceMonitor {
      constructor(options = {}) {
        this.debug = options.debug || false;
        this.reportEndpoint = options.reportEndpoint;
        this.metrics = {};
        this.init();
      }
      
      init() {
        // Wait for page to be fully loaded
        if (document.readyState === 'complete') {
          this.startMonitoring();
        } else {
          window.addEventListener('load', () => this.startMonitoring());
        }
      }
      
      startMonitoring() {
        this.measureNavigationTiming();
        this.measureCoreWebVitals();
        this.measureResourceTiming();
        this.setupPerformanceObserver();
        
        // Report metrics after a delay to capture all measurements
        setTimeout(() => this.reportMetrics(), 5000);
      }
      
      measureNavigationTiming() {
        if ('performance' in window && 'getEntriesByType' in performance) {
          const navigation = performance.getEntriesByType('navigation')[0];
          if (navigation) {
            this.metrics.navigationTiming = {
              dns: navigation.domainLookupEnd - navigation.domainLookupStart,
              tcp: navigation.connectEnd - navigation.connectStart,
              ttfb: navigation.responseStart - navigation.requestStart,
              download: navigation.responseEnd - navigation.responseStart,
              domInteractive: navigation.domInteractive - navigation.navigationStart,
              domComplete: navigation.domComplete - navigation.navigationStart,
              loadComplete: navigation.loadEventEnd - navigation.navigationStart
            };
            
            if (this.debug) {
              console.log('📊 Navigation Timing:', this.metrics.navigationTiming);
            }
          }
        }
      }
      
      measureCoreWebVitals() {
        // Largest Contentful Paint (LCP)
        if ('PerformanceObserver' in window) {
          try {
            const lcpObserver = new PerformanceObserver((list) => {
              const entries = list.getEntries();
              const lastEntry = entries[entries.length - 1];
              this.metrics.lcp = Math.round(lastEntry.startTime);
              
              if (this.debug) {
                console.log('🎯 LCP:', this.metrics.lcp + 'ms');
              }
            });
            lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
          } catch (e) {
            console.warn('LCP measurement not supported');
          }
          
          // First Input Delay (FID)
          try {
            const fidObserver = new PerformanceObserver((list) => {
              list.getEntries().forEach((entry) => {
                this.metrics.fid = Math.round(entry.processingStart - entry.startTime);
                
                if (this.debug) {
                  console.log('⚡ FID:', this.metrics.fid + 'ms');
                }
              });
            });
            fidObserver.observe({ entryTypes: ['first-input'] });
          } catch (e) {
            console.warn('FID measurement not supported');
          }
          
          // Cumulative Layout Shift (CLS)
          try {
            let clsValue = 0;
            const clsObserver = new PerformanceObserver((list) => {
              list.getEntries().forEach((entry) => {
                if (!entry.hadRecentInput) {
                  clsValue += entry.value;
                  this.metrics.cls = Math.round(clsValue * 1000) / 1000;
                  
                  if (this.debug) {
                    console.log('📐 CLS:', this.metrics.cls);
                  }
                }
              });
            });
            clsObserver.observe({ entryTypes: ['layout-shift'] });
          } catch (e) {
            console.warn('CLS measurement not supported');
          }
          
          // First Contentful Paint (FCP)
          try {
            const fcpObserver = new PerformanceObserver((list) => {
              list.getEntries().forEach((entry) => {
                if (entry.name === 'first-contentful-paint') {
                  this.metrics.fcp = Math.round(entry.startTime);
                  
                  if (this.debug) {
                    console.log('🎨 FCP:', this.metrics.fcp + 'ms');
                  }
                }
              });
            });
            fcpObserver.observe({ entryTypes: ['paint'] });
          } catch (e) {
            console.warn('FCP measurement not supported');
          }
        }
      }
      
      measureResourceTiming() {
        if ('performance' in window && 'getEntriesByType' in performance) {
          const resources = performance.getEntriesByType('resource');
          
          const resourceMetrics = {
            totalResources: resources.length,
            totalSize: 0,
            totalDuration: 0,
            byType: {}
          };
          
          resources.forEach(resource => {
            const type = this.getResourceType(resource.name);
            const size = resource.transferSize || 0;
            const duration = resource.responseEnd - resource.startTime;
            
            resourceMetrics.totalSize += size;
            resourceMetrics.totalDuration += duration;
            
            if (!resourceMetrics.byType[type]) {
              resourceMetrics.byType[type] = { count: 0, size: 0, duration: 0 };
            }
            
            resourceMetrics.byType[type].count++;
            resourceMetrics.byType[type].size += size;
            resourceMetrics.byType[type].duration += duration;
          });
          
          this.metrics.resources = resourceMetrics;
          
          if (this.debug) {
            console.log('📦 Resource Timing:', resourceMetrics);
          }
        }
      }
      
      getResourceType(url) {
        if (url.match(/\.(jpg|jpeg|png|gif|webp|svg|avif)$/i)) return 'image';
        if (url.match(/\.(css)$/i)) return 'stylesheet';
        if (url.match(/\.(js)$/i)) return 'script';
        if (url.match(/\.(woff|woff2|ttf|otf)$/i)) return 'font';
        return 'other';
      }
      
      setupPerformanceObserver() {
        if ('PerformanceObserver' in window) {
          // Monitor long tasks (> 50ms)
          try {
            const longTaskObserver = new PerformanceObserver((list) => {
              list.getEntries().forEach((entry) => {
                if (this.debug) {
                  console.warn('⚠️ Long Task detected:', entry.duration + 'ms');
                }
                
                if (!this.metrics.longTasks) {
                  this.metrics.longTasks = [];
                }
                
                this.metrics.longTasks.push({
                  duration: Math.round(entry.duration),
                  startTime: Math.round(entry.startTime)
                });
              });
            });
            longTaskObserver.observe({ entryTypes: ['longtask'] });
          } catch (e) {
            console.warn('Long task monitoring not supported');
          }
        }
      }
      
      getDeviceInfo() {
        return {
          userAgent: navigator.userAgent,
          viewport: {
            width: window.innerWidth,
            height: window.innerHeight
          },
          screen: {
            width: screen.width,
            height: screen.height,
            pixelRatio: window.devicePixelRatio || 1
          },
          connection: navigator.connection ? {
            effectiveType: navigator.connection.effectiveType,
            downlink: navigator.connection.downlink,
            rtt: navigator.connection.rtt
          } : null,
          memory: navigator.deviceMemory || null,
          cores: navigator.hardwareConcurrency || null
        };
      }
      
      async reportMetrics() {
        const report = {
          url: window.location.href,
          timestamp: Date.now(),
          metrics: this.metrics,
          device: this.getDeviceInfo(),
          performance: {
            score: this.calculatePerformanceScore(),
            grade: this.getPerformanceGrade()
          }
        };
        
        if (this.debug) {
          console.log('📈 Performance Report:', report);
        }
        
        // Send to analytics endpoint
        if (this.reportEndpoint) {
          try {
            await fetch(this.reportEndpoint, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify(report)
            });
          } catch (error) {
            if (this.debug) {
              console.error('Failed to send performance report:', error);
            }
          }
        }
        
        // Send to Google Analytics 4 (if available)
        if (typeof gtag !== 'undefined') {
          gtag('event', 'page_performance', {
            lcp: this.metrics.lcp,
            fid: this.metrics.fid,
            cls: this.metrics.cls,
            fcp: this.metrics.fcp,
            performance_score: report.performance.score
          });
        }
      }
      
      calculatePerformanceScore() {
        let score = 100;
        
        // LCP scoring (0-25 points)
        if (this.metrics.lcp) {
          if (this.metrics.lcp > 4000) score -= 25;
          else if (this.metrics.lcp > 2500) score -= 15;
          else if (this.metrics.lcp > 1200) score -= 5;
        }
        
        // FID scoring (0-25 points)
        if (this.metrics.fid) {
          if (this.metrics.fid > 300) score -= 25;
          else if (this.metrics.fid > 100) score -= 15;
          else if (this.metrics.fid > 50) score -= 5;
        }
        
        // CLS scoring (0-25 points)
        if (this.metrics.cls) {
          if (this.metrics.cls > 0.25) score -= 25;
          else if (this.metrics.cls > 0.1) score -= 15;
          else if (this.metrics.cls > 0.05) score -= 5;
        }
        
        // FCP scoring (0-25 points)
        if (this.metrics.fcp) {
          if (this.metrics.fcp > 3000) score -= 25;
          else if (this.metrics.fcp > 1800) score -= 15;
          else if (this.metrics.fcp > 900) score -= 5;
        }
        
        return Math.max(0, Math.round(score));
      }
      
      getPerformanceGrade() {
        const score = this.calculatePerformanceScore();
        if (score >= 90) return 'A';
        if (score >= 80) return 'B';
        if (score >= 70) return 'C';
        if (score >= 60) return 'D';
        return 'F';
      }
    }
    
    // Initialize performance monitoring
    new PerformanceMonitor({ debug, reportEndpoint });
  </script>
)}

<!-- Development performance overlay -->
{debug && (
  <div id="performance-overlay" class="fixed bottom-4 right-4 bg-black/90 text-white p-4 rounded-lg text-xs font-mono z-50 max-w-xs">
    <div class="mb-2 font-bold">Performance Metrics</div>
    <div id="performance-metrics">Loading...</div>
  </div>
  
  <script>
    // Update performance overlay in real-time
    function updatePerformanceOverlay() {
      const overlay = document.getElementById('performance-metrics');
      if (!overlay) return;
      
      const metrics = [];
      
      // Navigation timing
      if (performance.timing) {
        const timing = performance.timing;
        metrics.push(`TTFB: ${timing.responseStart - timing.navigationStart}ms`);
        metrics.push(`DOM: ${timing.domContentLoadedEventEnd - timing.navigationStart}ms`);
        metrics.push(`Load: ${timing.loadEventEnd - timing.navigationStart}ms`);
      }
      
      // Memory usage (if available)
      if (performance.memory) {
        const memory = performance.memory;
        metrics.push(`Memory: ${Math.round(memory.usedJSHeapSize / 1024 / 1024)}MB`);
      }
      
      overlay.innerHTML = metrics.join('<br>');
    }
    
    // Update overlay periodically
    setInterval(updatePerformanceOverlay, 1000);
    updatePerformanceOverlay();
  </script>
)}
