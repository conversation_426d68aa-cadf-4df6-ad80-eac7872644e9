// Analytics and conversion tracking
// Tracks user interactions with the word wall reduction improvements

interface AnalyticsEvent {
  event: string;
  category: string;
  action: string;
  label?: string;
  value?: number;
  customData?: Record<string, any>;
}

class Analytics {
  private events: AnalyticsEvent[] = [];
  private sessionId: string;
  private startTime: number;

  constructor() {
    this.sessionId = this.generateSessionId();
    this.startTime = Date.now();
    this.initializeTracking();
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private initializeTracking() {
    // Track page load
    this.track({
      event: 'page_view',
      category: 'engagement',
      action: 'page_load',
      customData: {
        timestamp: Date.now(),
        userAgent: navigator.userAgent,
        viewport: {
          width: window.innerWidth,
          height: window.innerHeight
        }
      }
    });

    // Track package interactions
    this.setupPackageTracking();
    
    // Track form interactions
    this.setupFormTracking();
    
    // Track scroll depth
    this.setupScrollTracking();
  }

  private setupPackageTracking() {
    // Track package card views
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const packageId = entry.target.getAttribute('data-package-id');
          if (packageId) {
            this.track({
              event: 'package_view',
              category: 'packages',
              action: 'card_viewed',
              label: packageId
            });
          }
        }
      });
    }, { threshold: 0.5 });

    // Observe package cards
    document.querySelectorAll('[data-package-id]').forEach(card => {
      observer.observe(card);
    });

    // Track package modal opens
    document.addEventListener('click', (e) => {
      const target = e.target as HTMLElement;
      const packageCard = target.closest('[data-package-id]');
      if (packageCard) {
        const packageId = packageCard.getAttribute('data-package-id');
        this.track({
          event: 'package_modal_open',
          category: 'packages',
          action: 'modal_opened',
          label: packageId
        });
      }
    });
  }

  private setupFormTracking() {
    // Track form interactions
    const form = document.querySelector('form[x-data*="contactForm"]');
    if (form) {
      // Track form start
      form.addEventListener('focusin', () => {
        this.track({
          event: 'form_start',
          category: 'conversion',
          action: 'form_interaction_start'
        });
      }, { once: true });

      // Track field interactions
      form.querySelectorAll('input, select, textarea').forEach(field => {
        field.addEventListener('blur', () => {
          const fieldName = field.getAttribute('name') || field.getAttribute('id');
          this.track({
            event: 'form_field_complete',
            category: 'conversion',
            action: 'field_completed',
            label: fieldName
          });
        });
      });

      // Track form submission
      form.addEventListener('submit', () => {
        this.track({
          event: 'form_submit',
          category: 'conversion',
          action: 'form_submitted'
        });
      });
    }
  }

  private setupScrollTracking() {
    let maxScroll = 0;
    const trackingPoints = [25, 50, 75, 90, 100];
    const tracked = new Set<number>();

    window.addEventListener('scroll', () => {
      const scrollPercent = Math.round(
        (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100
      );
      
      maxScroll = Math.max(maxScroll, scrollPercent);
      
      trackingPoints.forEach(point => {
        if (scrollPercent >= point && !tracked.has(point)) {
          tracked.add(point);
          this.track({
            event: 'scroll_depth',
            category: 'engagement',
            action: 'scroll_milestone',
            label: `${point}%`,
            value: point
          });
        }
      });
    });
  }

  public track(event: AnalyticsEvent) {
    // Add session data
    const enrichedEvent = {
      ...event,
      sessionId: this.sessionId,
      timestamp: Date.now(),
      timeOnPage: Date.now() - this.startTime
    };

    this.events.push(enrichedEvent);

    // Send to analytics service (placeholder)
    this.sendToAnalytics(enrichedEvent);
    
    // Log for development
    if (process.env.NODE_ENV === 'development') {
      console.log('📊 Analytics Event:', enrichedEvent);
    }
  }

  private sendToAnalytics(event: AnalyticsEvent) {
    // Placeholder for sending to analytics service
    // Could integrate with Google Analytics, Mixpanel, etc.
    
    // For now, store in localStorage for development
    const stored = localStorage.getItem('mpp_analytics') || '[]';
    const events = JSON.parse(stored);
    events.push(event);
    localStorage.setItem('mpp_analytics', JSON.stringify(events.slice(-100))); // Keep last 100 events
  }

  public getSessionData() {
    return {
      sessionId: this.sessionId,
      events: this.events,
      duration: Date.now() - this.startTime
    };
  }

  // Track specific word wall reduction metrics
  public trackContentOptimization(action: string, data?: Record<string, any>) {
    this.track({
      event: 'content_optimization',
      category: 'word_wall_reduction',
      action,
      customData: data
    });
  }

  // Track package selection efficiency
  public trackPackageSelection(packageId: string, timeToSelect: number) {
    this.track({
      event: 'package_selection',
      category: 'conversion_efficiency',
      action: 'package_selected',
      label: packageId,
      value: timeToSelect,
      customData: {
        selectionTime: timeToSelect,
        packageId
      }
    });
  }
}

// Initialize analytics
const analytics = new Analytics();

// Export for global use
(window as any).mppAnalytics = analytics;

export default analytics;
