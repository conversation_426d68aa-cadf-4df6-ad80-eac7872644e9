---
// Quantity Indicator component
// Visual representation of pumpkin quantities instead of text

export interface Props {
  quantity: string;
  class?: string;
  variant?: 'default' | 'compact' | 'minimal';
}

const { 
  quantity,
  class: className = '',
  variant = 'default'
} = Astro.props;

// Extract number range from quantity string
const getQuantityInfo = (qty: string) => {
  if (qty.includes('20+')) {
    return { min: 20, max: 25, display: '20+', level: 'high' };
  } else if (qty.includes('15-18')) {
    return { min: 15, max: 18, display: '15-18', level: 'medium-high' };
  } else if (qty.includes('8-12')) {
    return { min: 8, max: 12, display: '8-12', level: 'medium' };
  } else {
    return { min: 5, max: 8, display: '5-8', level: 'low' };
  }
};

const quantityInfo = getQuantityInfo(quantity);

// Get visual representation based on quantity level
const getPumpkinIcons = (level: string) => {
  switch (level) {
    case 'high':
      return ['🎃', '🎃', '🎃', '🎃', '🎃'];
    case 'medium-high':
      return ['🎃', '🎃', '🎃', '🎃'];
    case 'medium':
      return ['🎃', '🎃', '🎃'];
    default:
      return ['🎃', '🎃'];
  }
};

const getVariantClasses = () => {
  switch (variant) {
    case 'compact':
      return 'text-xs';
    case 'minimal':
      return 'text-xs';
    default:
      return 'text-sm';
  }
};

const pumpkinIcons = getPumpkinIcons(quantityInfo.level);
---

<div class:list={[
  'inline-flex items-center gap-1 bg-white/90 backdrop-blur-sm rounded-full px-3 py-1 shadow-sm',
  getVariantClasses(),
  className
]}>
  <!-- Pumpkin Icons -->
  <div class="flex items-center gap-0.5">
    {pumpkinIcons.map((icon, index) => (
      <span 
        class="text-orange-500 transition-all duration-300"
        style={`animation-delay: ${index * 100}ms`}
      >
        {icon}
      </span>
    ))}
    {quantityInfo.level === 'high' && (
      <span class="text-orange-500 font-bold ml-0.5">+</span>
    )}
  </div>
  
  <!-- Quantity Text -->
  <span class="font-medium text-gray-700 ml-1">
    {quantityInfo.display}
  </span>
</div>

<style>
  /* Subtle animation for pumpkin icons */
  @keyframes bounce-subtle {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-2px); }
  }
  
  .text-orange-500:hover {
    animation: bounce-subtle 0.6s ease-in-out;
  }
  
  /* Responsive adjustments */
  @media (max-width: 640px) {
    .gap-0.5 {
      gap: 0.125rem;
    }
    
    .px-3 {
      padding-left: 0.5rem;
      padding-right: 0.5rem;
    }
  }
</style>
