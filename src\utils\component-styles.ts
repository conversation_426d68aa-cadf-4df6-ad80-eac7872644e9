// Shared component styling utilities
// Eliminates duplicate styling patterns across components

export type ButtonVariant = 'primary' | 'secondary' | 'outline' | 'ghost' | 'toggle';
export type CardVariant = 'default' | 'hover' | 'modal' | 'compact';
export type FormVariant = 'default' | 'inline' | 'compact';
export type SectionVariant = 'default' | 'compact' | 'minimal';

/**
 * Generate button classes based on variant
 */
export function getButtonClasses(variant: ButtonVariant, active: boolean = false): string {
  const baseClasses = 'inline-flex items-center justify-center px-4 py-2 rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 tap-target';
  
  const variantClasses = {
    primary: 'bg-primary-500 text-white hover:bg-primary-600 focus:ring-primary-500 shadow-md hover:shadow-lg transform hover:scale-105',
    secondary: 'bg-secondary-500 text-white hover:bg-secondary-600 focus:ring-secondary-500 shadow-md hover:shadow-lg transform hover:scale-105',
    outline: 'border-2 border-primary-500 text-primary-500 hover:bg-primary-500 hover:text-white focus:ring-primary-500',
    ghost: 'text-primary-500 hover:bg-primary-50 focus:ring-primary-500',
    toggle: active 
      ? 'bg-primary-500 text-white shadow-md'
      : 'px-4 py-2 rounded-md text-sm font-semibold transition-all duration-200 text-gray-600 hover:text-primary-500 hover:bg-primary-50'
  };
  
  return `${baseClasses} ${variantClasses[variant]}`;
}

/**
 * Generate card classes based on variant
 */
export function getCardClasses(variant: CardVariant): string {
  const baseClasses = 'bg-white rounded-xl overflow-hidden';
  
  const variantClasses = {
    default: 'shadow-lg',
    hover: 'shadow-lg transition-all duration-300 hover:shadow-xl hover:scale-105 gpu-accelerated',
    modal: 'shadow-2xl max-w-lg w-full max-h-screen overflow-y-auto',
    compact: 'shadow-md rounded-lg'
  };
  
  return `${baseClasses} ${variantClasses[variant]}`;
}

/**
 * Generate form field classes
 */
export function getFormFieldClasses(variant: FormVariant = 'default'): {
  input: string;
  label: string;
  error: string;
  container: string;
} {
  const baseInput = 'w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors tap-target';
  const baseLabel = 'block text-sm font-medium text-gray-700 mb-2';
  const baseError = 'text-red-600 text-sm mt-1';
  
  const variants = {
    default: {
      input: `${baseInput} px-4 py-3`,
      label: baseLabel,
      error: baseError,
      container: 'space-y-1'
    },
    inline: {
      input: `${baseInput} px-3 py-2`,
      label: `${baseLabel} mb-1`,
      error: baseError,
      container: 'flex flex-col'
    },
    compact: {
      input: `${baseInput} px-3 py-2 text-sm`,
      label: `${baseLabel} text-xs mb-1`,
      error: `${baseError} text-xs`,
      container: 'space-y-1'
    }
  };
  
  return variants[variant];
}

/**
 * Generate section classes based on variant
 */
export function getSectionClasses(variant: SectionVariant = 'default'): string {
  const variants = {
    default: 'py-16 lg:py-24',
    compact: 'py-8 lg:py-12',
    minimal: 'py-6 lg:py-8'
  };
  
  return variants[variant];
}

/**
 * Generate modal overlay classes
 */
export function getModalClasses(): {
  overlay: string;
  content: string;
  closeButton: string;
} {
  return {
    overlay: 'fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4',
    content: 'bg-white rounded-xl shadow-2xl max-w-lg w-full max-h-screen overflow-y-auto',
    closeButton: 'absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors'
  };
}

/**
 * Generate responsive grid classes
 */
export function getGridClasses(
  cols: { mobile?: number; tablet?: number; desktop?: number } = {}
): string {
  const { mobile = 1, tablet = 2, desktop = 3 } = cols;
  
  const mobileClass = `grid-cols-${mobile}`;
  const tabletClass = tablet ? `md:grid-cols-${tablet}` : '';
  const desktopClass = desktop ? `lg:grid-cols-${desktop}` : '';
  
  return `grid gap-6 ${mobileClass} ${tabletClass} ${desktopClass}`.trim();
}

/**
 * Generate transition classes for animations
 */
export function getTransitionClasses(type: 'fade' | 'slide' | 'scale' | 'modal' = 'fade'): {
  enter: string;
  enterStart: string;
  enterEnd: string;
  leave: string;
  leaveStart: string;
  leaveEnd: string;
} {
  const transitions = {
    fade: {
      enter: 'transition ease-out duration-300',
      enterStart: 'opacity-0',
      enterEnd: 'opacity-100',
      leave: 'transition ease-in duration-200',
      leaveStart: 'opacity-100',
      leaveEnd: 'opacity-0'
    },
    slide: {
      enter: 'transition ease-out duration-300',
      enterStart: 'opacity-0 transform translate-y-4',
      enterEnd: 'opacity-100 transform translate-y-0',
      leave: 'transition ease-in duration-200',
      leaveStart: 'opacity-100 transform translate-y-0',
      leaveEnd: 'opacity-0 transform translate-y-4'
    },
    scale: {
      enter: 'transition ease-out duration-300',
      enterStart: 'opacity-0 transform scale-95',
      enterEnd: 'opacity-100 transform scale-100',
      leave: 'transition ease-in duration-200',
      leaveStart: 'opacity-100 transform scale-100',
      leaveEnd: 'opacity-0 transform scale-95'
    },
    modal: {
      enter: 'transition ease-out duration-300',
      enterStart: 'opacity-0',
      enterEnd: 'opacity-100',
      leave: 'transition ease-in duration-200',
      leaveStart: 'opacity-100',
      leaveEnd: 'opacity-0'
    }
  };
  
  return transitions[type];
}

/**
 * Generate loading state classes
 */
export function getLoadingClasses(): {
  container: string;
  spinner: string;
  overlay: string;
} {
  return {
    container: 'opacity-70 pointer-events-none',
    spinner: 'animate-spin rounded-full h-6 w-6 border-b-2 border-primary-500',
    overlay: 'absolute inset-0 bg-white/80 flex items-center justify-center z-10'
  };
}

/**
 * Generate text shadow classes for better readability
 */
export function getTextShadowClasses(size: 'sm' | 'md' | 'lg' = 'md'): string {
  const shadows = {
    sm: 'text-shadow-sm',
    md: 'text-shadow',
    lg: 'text-shadow-lg'
  };
  
  return shadows[size];
}

/**
 * Generate responsive container classes
 */
export function getContainerClasses(size: 'sm' | 'md' | 'lg' | 'xl' | 'full' = 'lg'): string {
  const baseClasses = 'mx-auto px-4 sm:px-6 lg:px-8';
  
  const sizeClasses = {
    sm: 'max-w-3xl',
    md: 'max-w-5xl',
    lg: 'max-w-7xl',
    xl: 'max-w-screen-2xl',
    full: 'max-w-none'
  };
  
  return `${baseClasses} ${sizeClasses[size]}`;
}

/**
 * Common class combinations for frequent patterns
 */
export const COMMON_PATTERNS = {
  // Hero section patterns
  heroTitle: 'text-3xl md:text-4xl lg:text-5xl font-serif font-bold text-gray-900 mb-6',
  heroSubtitle: 'text-lg md:text-xl text-gray-600 max-w-3xl mx-auto',
  
  // Section patterns
  sectionTitle: 'text-3xl md:text-4xl lg:text-5xl font-serif font-bold text-gray-900 mb-6',
  sectionSubtitle: 'text-lg md:text-xl text-gray-600 max-w-3xl mx-auto',
  
  // Navigation patterns
  navLink: 'text-gray-700 hover:text-primary-500 transition-colors duration-200 font-medium',
  navLinkActive: 'text-primary-500',
  
  // Image patterns
  imageOverlay: 'absolute inset-0 bg-black/20 transition-opacity',
  imageContainer: 'relative overflow-hidden',
  
  // Form patterns
  formGrid: 'grid md:grid-cols-2 gap-6',
  formSection: 'space-y-6',
  
  // Card patterns
  packageCard: 'bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-300 hover:shadow-xl hover:scale-105',
  
  // Mobile optimizations
  mobileFirst: 'block md:hidden',
  desktopOnly: 'hidden md:block',
  touchTarget: 'min-h-[44px] min-w-[44px]'
} as const;
