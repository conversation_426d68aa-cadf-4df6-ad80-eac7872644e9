---
// Contact Form component following SOLID principles
// Single Responsibility: Handle contact form submission and validation
// Open/Closed: Extensible through props and form fields
// Interface Segregation: Clean form interface

export interface Props {
  title?: string;
  subtitle?: string;
  submitText?: string;
  showPackageSelect?: boolean;
  showToggle?: boolean;
  class?: string;
}

const {
  title = 'Complete Your Order',
  subtitle = 'Ready to book? Complete your order with secure payment processing!',
  submitText = 'Complete Order',
  showPackageSelect = true,
  showToggle = true,
  class: className = ''
} = Astro.props;

// Package options for selection
const packageOptions = [
  { value: '', label: 'Select a package...' },
  { value: 'harvest-essentials', label: 'Harvest Essentials - $349' },
  { value: 'pumpkin-charm', label: 'Pumpkin Charm - $599' },
  { value: 'fall-favorites', label: 'Fall Favorites - $799' },
  { value: 'autumn-splendor', label: 'Autumn Splendor - $999' },
  { value: 'business-solutions', label: 'Business Solutions - Custom' },
  { value: 'event-decor', label: 'Event Decor - Custom' },
  { value: 'custom', label: 'Custom Package - Let\'s discuss!' }
];


---

<div class:list={['bg-white rounded-2xl shadow-xl p-6 md:p-8', className]}>
  <!-- Form Header with Toggle -->
  <div class="mb-8">
    {showToggle ? (
      <div class="flex flex-col md:flex-row justify-between items-center mb-6 gap-4">
        <h2 id="contact-title" class="text-2xl md:text-3xl font-serif font-bold text-gray-900">
          {title}
        </h2>
        <div class="flex bg-gray-100 rounded-lg p-1">
          <button
            type="button"
            id="order-toggle"
            class="toggle-btn active px-4 py-2 rounded-md text-sm font-semibold transition-all"
            data-mode="order"
          >
            💳 Order Now
          </button>
          <button
            type="button"
            id="quote-toggle"
            class="toggle-btn px-4 py-2 rounded-md text-sm font-semibold transition-all"
            data-mode="quote"
          >
            📋 Free Quote
          </button>
        </div>
      </div>
    ) : (
      <div class="text-center mb-6">
        <h2 class="text-2xl md:text-3xl font-serif font-bold text-gray-900 mb-3">
          {title}
        </h2>
      </div>
    )}
    <p id="contact-subtitle" class="text-gray-600 max-w-2xl mx-auto text-center">
      {subtitle}
    </p>
  </div>
  
  <!-- Contact Form -->
  <form
    x-data="contactForm()"
    @submit.prevent="submitForm('/api/contact')"
    @keydown.enter="handleEnterKey($event)"
    class="space-y-6"
  >
    <!-- Package Selection -->
    {showPackageSelect && (
      <div>
        <label for="package" class="form-label">
          Interested Package
        </label>
        <select 
          id="package"
          name="package"
          x-model="formData.package"
          @change="validateField('package', $event.target.value, ['required'])"
          class="form-input"
          required
        >
          {packageOptions.map(option => (
            <option value={option.value}>{option.label}</option>
          ))}
        </select>
        <p x-show="errors.package" x-text="errors.package" class="form-error" style="display: none;"></p>
      </div>
    )}
    
    <!-- Personal Information -->
    <div class="grid md:grid-cols-2 gap-6">
      <!-- First Name -->
      <div>
        <label for="firstName" class="form-label">
          First Name *
        </label>
        <input
          type="text"
          id="firstName"
          name="firstName"
          autocomplete="given-name"
          x-model="formData.firstName"
          @blur="validateField('firstName', $event.target.value, ['required'])"
          class="form-input"
          placeholder="Enter your first name"
          required
        />
        <p x-show="errors.firstName" x-text="errors.firstName" class="form-error" style="display: none;"></p>
      </div>

      <!-- Last Name -->
      <div>
        <label for="lastName" class="form-label">
          Last Name *
        </label>
        <input
          type="text"
          id="lastName"
          name="lastName"
          autocomplete="family-name"
          x-model="formData.lastName"
          @blur="validateField('lastName', $event.target.value, ['required'])"
          class="form-input"
          placeholder="Enter your last name"
          required
        />
        <p x-show="errors.lastName" x-text="errors.lastName" class="form-error" style="display: none;"></p>
      </div>
    </div>
    
    <!-- Contact Information -->
    <div class="grid md:grid-cols-2 gap-6">
      <!-- Email -->
      <div>
        <label for="email" class="form-label">
          Email Address *
        </label>
        <input
          type="email"
          id="email"
          name="email"
          autocomplete="email"
          x-model="formData.email"
          @blur="validateField('email', $event.target.value, ['required', 'email'])"
          class="form-input"
          placeholder="<EMAIL>"
          required
        />
        <p x-show="errors.email" x-text="errors.email" class="form-error" style="display: none;"></p>
      </div>

      <!-- Phone -->
      <div>
        <label for="phone" class="form-label">
          Phone Number *
        </label>
        <input
          type="tel"
          id="phone"
          name="phone"
          autocomplete="tel"
          x-model="formData.phone"
          @blur="validateField('phone', $event.target.value, ['required', 'phone'])"
          @input="formData.phone = formatPhone($event.target.value)"
          class="form-input"
          placeholder="(*************"
          required
        />
        <p x-show="errors.phone" x-text="errors.phone" class="form-error" style="display: none;"></p>
      </div>
    </div>
    
    <!-- Address Information -->
    <div class="space-y-4">
      <h3 class="text-lg font-semibold text-gray-900">Delivery Address *</h3>

      <!-- Street Address -->
      <div>
        <label for="streetAddress" class="form-label">
          Street Address *
        </label>
        <input
          type="text"
          id="streetAddress"
          name="streetAddress"
          autocomplete="street-address"
          x-model="formData.streetAddress"
          @blur="validateField('streetAddress', $event.target.value, ['required']); validateFullAddress()"
          class="form-input"
          placeholder="123 Main Street"
          required
        />
        <p x-show="errors.streetAddress" x-text="errors.streetAddress" class="form-error" style="display: none;"></p>
      </div>

      <!-- City, State, ZIP Grid -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <!-- City -->
        <div>
          <label for="city" class="form-label">
            City *
          </label>
          <input
            type="text"
            id="city"
            name="city"
            autocomplete="address-level2"
            x-model="formData.city"
            @blur="validateField('city', $event.target.value, ['required']); validateFullAddress()"
            class="form-input"
            placeholder="Salt Lake City"
            required
          />
          <p x-show="errors.city" x-text="errors.city" class="form-error" style="display: none;"></p>
        </div>

        <!-- State -->
        <div>
          <label for="state" class="form-label">
            State *
          </label>
          <select
            id="state"
            name="state"
            autocomplete="address-level1"
            x-model="formData.state"
            @change="validateField('state', $event.target.value, ['required']); validateFullAddress()"
            class="form-input"
            required
          >
            <option value="">Select State</option>
            <option value="AL">Alabama</option>
            <option value="AK">Alaska</option>
            <option value="AZ">Arizona</option>
            <option value="AR">Arkansas</option>
            <option value="CA">California</option>
            <option value="CO">Colorado</option>
            <option value="CT">Connecticut</option>
            <option value="DE">Delaware</option>
            <option value="FL">Florida</option>
            <option value="GA">Georgia</option>
            <option value="HI">Hawaii</option>
            <option value="ID">Idaho</option>
            <option value="IL">Illinois</option>
            <option value="IN">Indiana</option>
            <option value="IA">Iowa</option>
            <option value="KS">Kansas</option>
            <option value="KY">Kentucky</option>
            <option value="LA">Louisiana</option>
            <option value="ME">Maine</option>
            <option value="MD">Maryland</option>
            <option value="MA">Massachusetts</option>
            <option value="MI">Michigan</option>
            <option value="MN">Minnesota</option>
            <option value="MS">Mississippi</option>
            <option value="MO">Missouri</option>
            <option value="MT">Montana</option>
            <option value="NE">Nebraska</option>
            <option value="NV">Nevada</option>
            <option value="NH">New Hampshire</option>
            <option value="NJ">New Jersey</option>
            <option value="NM">New Mexico</option>
            <option value="NY">New York</option>
            <option value="NC">North Carolina</option>
            <option value="ND">North Dakota</option>
            <option value="OH">Ohio</option>
            <option value="OK">Oklahoma</option>
            <option value="OR">Oregon</option>
            <option value="PA">Pennsylvania</option>
            <option value="RI">Rhode Island</option>
            <option value="SC">South Carolina</option>
            <option value="SD">South Dakota</option>
            <option value="TN">Tennessee</option>
            <option value="TX">Texas</option>
            <option value="UT">Utah</option>
            <option value="VT">Vermont</option>
            <option value="VA">Virginia</option>
            <option value="WA">Washington</option>
            <option value="WV">West Virginia</option>
            <option value="WI">Wisconsin</option>
            <option value="WY">Wyoming</option>
          </select>
          <p x-show="errors.state" x-text="errors.state" class="form-error" style="display: none;"></p>
        </div>

        <!-- ZIP Code -->
        <div>
          <label for="zipCode" class="form-label">
            ZIP Code *
          </label>
          <input
            type="text"
            id="zipCode"
            name="zipCode"
            autocomplete="postal-code"
            x-model="formData.zipCode"
            @blur="validateField('zipCode', $event.target.value, ['required']); validateFullAddress()"
            @input="formData.zipCode = $event.target.value.replace(/\D/g, '').slice(0, 5)"
            class="form-input"
            placeholder="84101"
            maxlength="5"
            required
          />
          <p x-show="errors.zipCode" x-text="errors.zipCode" class="form-error" style="display: none;"></p>
        </div>
      </div>

      <!-- Address Validation Status -->
      <div x-show="addressValidation.message" class="mt-4 p-4 rounded-lg border"
           :class="{
             'bg-green-50 border-green-200': addressValidation.isValid === true,
             'bg-red-50 border-red-200': addressValidation.isValid === false,
             'bg-yellow-50 border-yellow-200': addressValidation.isValidating
           }"
           style="display: none;">
        <div class="flex items-center">
          <!-- Loading Spinner -->
          <svg x-show="addressValidation.isValidating" class="animate-spin h-5 w-5 mr-2"
               :class="{
                 'text-green-500': addressValidation.isValid === true,
                 'text-red-500': addressValidation.isValid === false,
                 'text-yellow-500': addressValidation.isValidating
               }"
               xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" style="display: none;">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>

          <!-- Success Icon -->
          <svg x-show="addressValidation.isValid === true" class="h-5 w-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20" style="display: none;">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          </svg>

          <!-- Error Icon -->
          <svg x-show="addressValidation.isValid === false" class="h-5 w-5 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20" style="display: none;">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>

          <div>
            <p x-text="addressValidation.message"
               :class="{
                 'text-green-700': addressValidation.isValid === true,
                 'text-red-700': addressValidation.isValid === false,
                 'text-yellow-700': addressValidation.isValidating
               }"
               class="text-sm font-medium"></p>
            <p x-show="addressValidation.suggestedArea"
               x-text="'Suggested service area: ' + addressValidation.suggestedArea"
               class="text-sm text-blue-600 mt-1"
               style="display: none;"></p>
          </div>
        </div>
      </div>
    </div>
    

    
    <!-- Special Requests -->
    <div>
      <label for="message" class="form-label">
        Special Requests or Questions
      </label>
      <textarea 
        id="message"
        name="message"
        x-model="formData.message"
        rows="4"
        class="form-input resize-none"
        placeholder="Tell us about your space, any special requirements, or questions you have..."
      ></textarea>
    </div>
    
    <!-- Payment Section (shown only in Order Now mode) -->
    <div id="payment-section" class="payment-section hidden" x-show="formMode === 'order'" style="display: none;">
      <div class="text-center mb-6">
        <h3 class="text-xl font-bold text-primary-600 mb-2">💳 Payment Information</h3>
        <p class="text-gray-600 text-sm">Complete your order with secure payment processing</p>
      </div>

      <div id="card-element" class="p-4 border border-gray-300 rounded-lg bg-gray-50">
        <!-- Stripe Elements will create form elements here -->
      </div>

      <div id="card-errors" role="alert" class="text-red-600 text-sm mt-2"></div>
    </div>

    <!-- Marketing Consent -->
    <div class="flex items-start space-x-3">
      <input
        type="checkbox"
        id="marketingConsent"
        name="marketingConsent"
        x-model="formData.marketingConsent"
        checked
        class="mt-1 h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
      />
      <label for="marketingConsent" class="text-sm text-gray-600">
        ✓ I'd like to receive updates about seasonal offerings and special promotions from Mountain Porch Pumpkins.
      </label>
    </div>
    
    <!-- Submit Button -->
    <div class="pt-4">
      <button
        type="submit"
        id="submit-button"
        :disabled="loading"
        class="w-full bg-primary-500 hover:bg-primary-600 disabled:bg-gray-400 text-white py-4 px-6 rounded-lg font-semibold text-lg transition-all transform hover:scale-105 tap-target focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
        :class="{ 'loading': loading }"
      >
        <span x-show="!loading" id="button-text">{submitText}</span>
        <span x-show="loading" class="flex items-center justify-center" style="display: none;">
          <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span x-text="formMode === 'order' ? 'Processing Payment...' : 'Sending...'"></span>
        </span>
      </button>
    </div>
    
    <!-- Privacy Notice -->
    <div class="text-center pt-4">
      <p class="text-xs text-gray-500">
        By submitting this form, you agree to our 
        <a href="/privacy" class="text-primary-600 hover:text-primary-700 underline">Privacy Policy</a>
        and 
        <a href="/terms" class="text-primary-600 hover:text-primary-700 underline">Terms of Service</a>.
        We'll contact you within 24 hours with your personalized quote.
      </p>
    </div>
  </form>
</div>

<!-- Contact form functionality is handled by Alpine.js in alpine.ts -->
