// Navigation store - Single Responsibility Principle
// Handles mobile menu and navigation state

export interface NavigationStore {
  mobileMenuOpen: boolean;
  toggleMobileMenu(): void;
  closeMobileMenu(): void;
  scrollToSection(sectionId: string): void;
}

export function createNavigationStore(): NavigationStore {
  return {
    mobileMenuOpen: false,

    toggleMobileMenu() {
      this.mobileMenuOpen = !this.mobileMenuOpen;
      
      // Prevent body scroll when menu is open (mobile UX)
      if (this.mobileMenuOpen) {
        document.body.style.overflow = 'hidden';
      } else {
        document.body.style.overflow = '';
      }
    },

    closeMobileMenu() {
      this.mobileMenuOpen = false;
      document.body.style.overflow = '';
    },

    // Handle smooth scrolling for mobile
    scrollToSection(sectionId: string) {
      const element = document.getElementById(sectionId);
      if (element) {
        element.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
        this.closeMobileMenu();
      }
    }
  };
}
