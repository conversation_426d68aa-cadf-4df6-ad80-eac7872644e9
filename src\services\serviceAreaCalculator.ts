// Service Area Calculator for Mountain Porch Pumpkins
// Automatically determines if an address falls within service areas

export interface ServiceArea {
  id: string;
  name: string;
  state: string;
  bounds: {
    north: number;
    south: number;
    east: number;
    west: number;
  };
  cities: string[];
  zipCodes?: string[];
}

export interface AddressValidationResult {
  isValid: boolean;
  serviceArea?: ServiceArea;
  suggestedArea?: string;
  message: string;
  coordinates?: {
    lat: number;
    lng: number;
  };
}

// Define Mountain Porch Pumpkins service areas with geographic boundaries
export const SERVICE_AREAS: ServiceArea[] = [
  {
    id: 'salt-lake-city',
    name: 'Salt Lake City (Wasatch Front)',
    state: 'UT',
    bounds: {
      north: 41.2,
      south: 40.4,
      east: -111.5,
      west: -112.2
    },
    cities: ['salt lake city', 'west valley city', 'west jordan', 'sandy', 'murray', 'midvale', 'cottonwood heights', 'millcreek', 'taylorsville', 'kearns', 'magna', 'riverton', 'draper', 'south jordan', 'herriman', 'holladay', 'south salt lake', 'bluffdale', 'alta', 'snowbird'],
    zipCodes: ['84101', '84102', '84103', '84104', '84105', '84106', '84107', '84108', '84109', '84110', '84111', '84112', '84113', '84114', '84115', '84116', '84117', '84118', '84119', '84120', '84121', '84122', '84123', '84124', '84125', '84126', '84127', '84128', '84129', '84130', '84131', '84132', '84133', '84134', '84135', '84136', '84138', '84139', '84141', '84143', '84144', '84145', '84147', '84148', '84150', '84151', '84152', '84157', '84158', '84165', '84170', '84171', '84180', '84184', '84190', '84199']
  },
  {
    id: 'park-city',
    name: 'Park City',
    state: 'UT',
    bounds: {
      north: 40.8,
      south: 40.5,
      east: -111.3,
      west: -111.7
    },
    cities: ['park city', 'deer valley', 'canyons', 'snyderville', 'kimball junction']
  },
  {
    id: 'ogden',
    name: 'Ogden',
    state: 'UT',
    bounds: {
      north: 41.4,
      south: 41.0,
      east: -111.7,
      west: -112.1
    },
    cities: ['ogden', 'roy', 'clearfield', 'layton', 'kaysville', 'farmington', 'bountiful', 'north salt lake', 'woods cross', 'centerville']
  },
  {
    id: 'provo',
    name: 'Provo (Utah Valley)',
    state: 'UT',
    bounds: {
      north: 40.4,
      south: 39.9,
      east: -111.4,
      west: -111.9
    },
    cities: ['provo', 'orem', 'springville', 'spanish fork', 'payson', 'american fork', 'pleasant grove', 'lehi', 'highland', 'alpine', 'cedar hills', 'saratoga springs']
  },
  {
    id: 'st-george',
    name: 'St. George',
    state: 'UT',
    bounds: {
      north: 37.3,
      south: 36.9,
      east: -113.3,
      west: -113.8
    },
    cities: ['st. george', 'saint george', 'washington', 'hurricane', 'ivins', 'santa clara', 'la verkin']
  },
  {
    id: 'cache-valley',
    name: 'Cache Valley (Logan)',
    state: 'UT',
    bounds: {
      north: 42.0,
      south: 41.5,
      east: -111.6,
      west: -112.0
    },
    cities: ['logan', 'north logan', 'hyde park', 'smithfield', 'providence', 'river heights', 'millville', 'nibley']
  },
  {
    id: 'moab',
    name: 'Moab',
    state: 'UT',
    bounds: {
      north: 38.8,
      south: 38.4,
      east: -109.3,
      west: -109.8
    },
    cities: ['moab']
  },
  {
    id: 'jackson-hole',
    name: 'Jackson Hole',
    state: 'WY',
    bounds: {
      north: 43.8,
      south: 43.3,
      east: -110.5,
      west: -111.0
    },
    cities: ['jackson', 'teton village', 'wilson', 'hoback', 'moose']
  },
  {
    id: 'star-valley',
    name: 'Star Valley',
    state: 'WY',
    bounds: {
      north: 43.0,
      south: 42.5,
      east: -110.8,
      west: -111.2
    },
    cities: ['afton', 'alpine', 'bedford', 'etna', 'fairview', 'freedom', 'grover', 'smoot', 'star valley ranch', 'thayne']
  },
  {
    id: 'boise',
    name: 'Boise (Treasure Valley)',
    state: 'ID',
    bounds: {
      north: 43.8,
      south: 43.4,
      east: -116.0,
      west: -116.8
    },
    cities: ['boise', 'meridian', 'nampa', 'caldwell', 'eagle', 'kuna', 'star', 'middleton', 'garden city']
  },
  {
    id: 'sun-valley',
    name: 'Sun Valley',
    state: 'ID',
    bounds: {
      north: 43.8,
      south: 43.6,
      east: -114.2,
      west: -114.5
    },
    cities: ['sun valley', 'ketchum', 'hailey', 'bellevue', 'elkhorn']
  },
  {
    id: 'rexburg',
    name: 'Rexburg',
    state: 'ID',
    bounds: {
      north: 43.9,
      south: 43.7,
      east: -111.7,
      west: -111.9
    },
    cities: ['rexburg', 'rigby', 'sugar city', 'teton']
  },
  {
    id: 'idaho-falls',
    name: 'Idaho Falls',
    state: 'ID',
    bounds: {
      north: 43.6,
      south: 43.4,
      east: -111.8,
      west: -112.2
    },
    cities: ['idaho falls', 'ammon', 'iona', 'ucon', 'shelley']
  },
  {
    id: 'twin-falls',
    name: 'Twin Falls',
    state: 'ID',
    bounds: {
      north: 42.7,
      south: 42.4,
      east: -114.2,
      west: -114.6
    },
    cities: ['twin falls', 'jerome', 'kimberly', 'filer', 'hansen']
  },
  {
    id: 'pocatello',
    name: 'Pocatello',
    state: 'ID',
    bounds: {
      north: 42.9,
      south: 42.7,
      east: -112.3,
      west: -112.6
    },
    cities: ['pocatello', 'chubbuck', 'inkom', 'mccammon']
  }
];

// Enhanced geocoding with multiple fallback services and better parsing
export async function geocodeAddress(address: string): Promise<{ lat: number; lng: number } | null> {
  // Clean and normalize the address
  const cleanAddress = normalizeAddress(address);

  // Try multiple geocoding strategies
  const strategies = [
    () => geocodeWithNominatim(cleanAddress),
    () => geocodeWithNominatimStructured(cleanAddress),
    () => geocodeWithFallbackParsing(cleanAddress)
  ];

  for (const strategy of strategies) {
    try {
      const result = await strategy();
      if (result) {
        console.log('Geocoding successful:', result);
        return result;
      }
    } catch (error) {
      console.warn('Geocoding strategy failed:', error);
      continue;
    }
  }

  return null;
}

// Normalize address for better geocoding
function normalizeAddress(address: string): string {
  return address
    .trim()
    .replace(/\s+/g, ' ') // Multiple spaces to single space
    .replace(/,\s*,/g, ',') // Remove double commas
    .replace(/\b(street|st\.?)\b/gi, 'Street')
    .replace(/\b(avenue|ave\.?)\b/gi, 'Avenue')
    .replace(/\b(drive|dr\.?)\b/gi, 'Drive')
    .replace(/\b(boulevard|blvd\.?)\b/gi, 'Boulevard')
    .replace(/\b(road|rd\.?)\b/gi, 'Road')
    .replace(/\b(lane|ln\.?)\b/gi, 'Lane')
    .replace(/\b(court|ct\.?)\b/gi, 'Court')
    .replace(/\b(place|pl\.?)\b/gi, 'Place');
}

// Primary geocoding with Nominatim
async function geocodeWithNominatim(address: string): Promise<{ lat: number; lng: number } | null> {
  const encodedAddress = encodeURIComponent(address);

  // Create abort controller for timeout
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

  try {
    const response = await fetch(
      `https://nominatim.openstreetmap.org/search?format=json&q=${encodedAddress}&limit=3&countrycodes=us&addressdetails=1`,
      {
        headers: {
          'User-Agent': 'MountainPorchPumpkins/1.0'
        },
        signal: controller.signal
      }
    );

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error('Nominatim service unavailable');
    }
  } catch (error) {
    clearTimeout(timeoutId);
    if (error.name === 'AbortError') {
      throw new Error('Geocoding request timed out');
    }
    throw error;
  }

  const data = await response.json();

  if (data && data.length > 0) {
    // Prefer results with higher importance or better address match
    const bestResult = data.find(result =>
      result.importance > 0.5 ||
      result.address?.state ||
      result.address?.city
    ) || data[0];

    return {
      lat: parseFloat(bestResult.lat),
      lng: parseFloat(bestResult.lon)
    };
  }

  return null;
}

// Structured geocoding with parsed address components
async function geocodeWithNominatimStructured(address: string): Promise<{ lat: number; lng: number } | null> {
  const parsed = parseAddressComponents(address);
  if (!parsed.city || !parsed.state) {
    return null;
  }

  // Build structured query
  const params = new URLSearchParams({
    format: 'json',
    limit: '3',
    countrycodes: 'us',
    addressdetails: '1'
  });

  if (parsed.houseNumber && parsed.street) {
    params.append('street', `${parsed.houseNumber} ${parsed.street}`);
  }
  params.append('city', parsed.city);
  params.append('state', parsed.state);
  if (parsed.zipCode) {
    params.append('postalcode', parsed.zipCode);
  }

  const response = await fetch(
    `https://nominatim.openstreetmap.org/search?${params.toString()}`,
    {
      headers: {
        'User-Agent': 'MountainPorchPumpkins/1.0'
      }
    }
  );

  if (!response.ok) {
    throw new Error('Structured geocoding failed');
  }

  const data = await response.json();

  if (data && data.length > 0) {
    return {
      lat: parseFloat(data[0].lat),
      lng: parseFloat(data[0].lon)
    };
  }

  return null;
}

// Fallback geocoding with simplified address
async function geocodeWithFallbackParsing(address: string): Promise<{ lat: number; lng: number } | null> {
  const parsed = parseAddressComponents(address);

  // Try just city, state
  if (parsed.city && parsed.state) {
    const simplifiedAddress = `${parsed.city}, ${parsed.state}`;
    return await geocodeWithNominatim(simplifiedAddress);
  }

  return null;
}

// Check if coordinates fall within a service area
export function isWithinServiceArea(lat: number, lng: number, serviceArea: ServiceArea): boolean {
  return (
    lat >= serviceArea.bounds.south &&
    lat <= serviceArea.bounds.north &&
    lng >= serviceArea.bounds.west &&
    lng <= serviceArea.bounds.east
  );
}

// Enhanced address component parsing
interface AddressComponents {
  houseNumber?: string;
  street?: string;
  city?: string;
  state?: string;
  zipCode?: string;
}

function parseAddressComponents(address: string): AddressComponents {
  const parts = address.split(',').map(part => part.trim());
  const result: AddressComponents = {};

  // Extract ZIP code from last part
  if (parts.length > 0) {
    const lastPart = parts[parts.length - 1];
    const zipMatch = lastPart.match(/\b(\d{5}(?:-\d{4})?)\b/);
    if (zipMatch) {
      result.zipCode = zipMatch[1];
      parts[parts.length - 1] = lastPart.replace(zipMatch[0], '').trim();
    }
  }

  // Extract state from last part
  if (parts.length > 0) {
    const lastPart = parts[parts.length - 1];
    const stateMatch = lastPart.match(/\b([A-Z]{2})\b/i);
    if (stateMatch) {
      result.state = stateMatch[1].toUpperCase();
      parts[parts.length - 1] = lastPart.replace(stateMatch[0], '').trim();
    }
  }

  // City is typically the second-to-last part
  if (parts.length >= 2) {
    result.city = parts[parts.length - 2].trim();
  } else if (parts.length === 1 && parts[0]) {
    // If only one part remains, it might be the city
    result.city = parts[0].trim();
  }

  // Street address is typically the first part
  if (parts.length >= 1 && parts[0]) {
    const streetPart = parts[0].trim();
    const streetMatch = streetPart.match(/^(\d+)\s+(.+)$/);
    if (streetMatch) {
      result.houseNumber = streetMatch[1];
      result.street = streetMatch[2];
    } else {
      result.street = streetPart;
    }
  }

  return result;
}

// Find service area by ZIP code
export function findServiceAreaByZipCode(zipCode: string): ServiceArea | null {
  const cleanZip = zipCode.replace(/[^0-9]/g, '').substring(0, 5);

  for (const area of SERVICE_AREAS) {
    if (area.zipCodes && area.zipCodes.includes(cleanZip)) {
      return area;
    }
  }

  return null;
}

// Enhanced city matching with fuzzy logic and aliases
export function findServiceAreaByCity(cityName: string, state?: string): ServiceArea | null {
  const normalizedCity = cityName.toLowerCase().trim();

  // City aliases and common variations
  const cityAliases: Record<string, string[]> = {
    'salt lake city': ['slc', 'salt lake', 'saltlake'],
    'park city': ['parkcity'],
    'jackson': ['jackson hole'],
    'jackson hole': ['jackson'],
    'st. george': ['saint george', 'st george'],
    'saint george': ['st. george', 'st george'],
    'sun valley': ['sunvalley'],
    'idaho falls': ['idahofalls'],
    'twin falls': ['twinfalls'],
    'cache valley': ['logan'],
    'logan': ['cache valley']
  };

  for (const area of SERVICE_AREAS) {
    if (state && area.state.toLowerCase() !== state.toLowerCase()) {
      continue;
    }

    // Direct city match
    if (area.cities.some(city => {
      const normalizedAreaCity = city.toLowerCase();
      return normalizedAreaCity === normalizedCity ||
             normalizedAreaCity.includes(normalizedCity) ||
             normalizedCity.includes(normalizedAreaCity);
    })) {
      return area;
    }

    // Check aliases
    for (const [canonical, aliases] of Object.entries(cityAliases)) {
      if (aliases.includes(normalizedCity) || canonical === normalizedCity) {
        if (area.cities.some(city => city.toLowerCase().includes(canonical) || canonical.includes(city.toLowerCase()))) {
          return area;
        }
      }
    }

    // Fuzzy matching for common misspellings
    if (area.cities.some(city => {
      const normalizedAreaCity = city.toLowerCase();
      return levenshteinDistance(normalizedAreaCity, normalizedCity) <= 2 && normalizedCity.length > 3;
    })) {
      return area;
    }
  }

  return null;
}

// Simple Levenshtein distance for fuzzy matching
function levenshteinDistance(str1: string, str2: string): number {
  const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));

  for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
  for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;

  for (let j = 1; j <= str2.length; j++) {
    for (let i = 1; i <= str1.length; i++) {
      const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
      matrix[j][i] = Math.min(
        matrix[j][i - 1] + 1,
        matrix[j - 1][i] + 1,
        matrix[j - 1][i - 1] + indicator
      );
    }
  }

  return matrix[str2.length][str1.length];
}

// Main function to validate an address and determine service area
export async function validateServiceArea(address: string): Promise<AddressValidationResult> {
  try {
    // Validate input
    if (!address || address.trim().length < 5) {
      return {
        isValid: false,
        message: 'Please enter a complete address with city and state.',
      };
    }

    const cleanAddress = address.trim();
    console.log('Validating address:', cleanAddress);

    // Parse address components
    const addressComponents = parseAddressComponents(cleanAddress);
    console.log('Parsed components:', addressComponents);

    // Try to find service area by ZIP code first (most reliable)
    if (addressComponents.zipCode) {
      const serviceAreaByZip = findServiceAreaByZipCode(addressComponents.zipCode);
      if (serviceAreaByZip) {
        console.log('Found service area by ZIP code:', serviceAreaByZip.name);
        return {
          isValid: true,
          serviceArea: serviceAreaByZip,
          message: `Great! We serve ${serviceAreaByZip.name}.`,
          coordinates: undefined
        };
      }
    }

    // Try to find service area by city name (fast and reliable)
    if (addressComponents.city) {
      const serviceAreaByCity = findServiceAreaByCity(addressComponents.city, addressComponents.state);
      if (serviceAreaByCity) {
        console.log('Found service area by city:', serviceAreaByCity.name);
        return {
          isValid: true,
          serviceArea: serviceAreaByCity,
          message: `Great! We serve ${serviceAreaByCity.name}.`,
          coordinates: undefined
        };
      }
    }

    // If state is provided but not in our service states, fail fast
    if (addressComponents.state) {
      const serviceStates = ['UT', 'ID', 'WY'];
      if (!serviceStates.includes(addressComponents.state.toUpperCase())) {
        return {
          isValid: false,
          message: `Sorry, we don't currently serve ${addressComponents.state}. We serve Utah, Idaho, and Wyoming.`,
        };
      }
    }

    // Try geocoding with multiple strategies
    console.log('Attempting geocoding...');
    const coordinates = await geocodeAddress(cleanAddress);

    if (!coordinates) {
      // If geocoding fails but we have city/state, try a more helpful message
      if (addressComponents.city && addressComponents.state) {
        const serviceStates = ['UT', 'ID', 'WY'];
        if (serviceStates.includes(addressComponents.state.toUpperCase())) {
          return {
            isValid: false,
            message: `We couldn't verify the exact address, but we do serve ${addressComponents.state}. Please contact us directly to confirm service availability.`,
          };
        }
      }

      return {
        isValid: false,
        message: 'Unable to verify this address. Please check the format (e.g., "123 Main St, City, State 12345") and try again.',
      };
    }

    console.log('Geocoding successful:', coordinates);

    // Check if coordinates fall within any service area
    for (const serviceArea of SERVICE_AREAS) {
      if (isWithinServiceArea(coordinates.lat, coordinates.lng, serviceArea)) {
        console.log('Address is within service area:', serviceArea.name);
        return {
          isValid: true,
          serviceArea,
          message: `Perfect! We serve ${serviceArea.name}.`,
          coordinates
        };
      }
    }

    // Not in service area - find nearest and provide helpful message
    let nearestArea: ServiceArea | null = null;
    let minDistance = Infinity;

    for (const serviceArea of SERVICE_AREAS) {
      const centerLat = (serviceArea.bounds.north + serviceArea.bounds.south) / 2;
      const centerLng = (serviceArea.bounds.east + serviceArea.bounds.west) / 2;
      const distance = Math.sqrt(
        Math.pow(coordinates.lat - centerLat, 2) + Math.pow(coordinates.lng - centerLng, 2)
      );

      if (distance < minDistance) {
        minDistance = distance;
        nearestArea = serviceArea;
      }
    }

    console.log('Address outside service area. Nearest:', nearestArea?.name);

    return {
      isValid: false,
      suggestedArea: nearestArea?.name,
      message: `Sorry, we don't currently serve this area. The nearest service area is ${nearestArea?.name}. Contact us - we may be able to arrange special delivery!`,
      coordinates
    };

  } catch (error) {
    console.error('Service area validation error:', error);
    return {
      isValid: false,
      message: 'Unable to verify service area at the moment. Please try again or contact us directly.',
    };
  }
}
