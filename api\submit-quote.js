// Simple quote submission handler
// This is a basic implementation - in production you'd want to integrate with your CRM/email system

export default async function handler(req, res) {
    // Set CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

    // Handle preflight requests
    if (req.method === 'OPTIONS') {
        res.status(200).end();
        return;
    }

    if (req.method !== 'POST') {
        res.status(405).json({ error: 'Method not allowed' });
        return;
    }

    try {
        const { customerInfo, packageDetails } = req.body;

        // Validate required fields
        if (!customerInfo || !packageDetails) {
            res.status(400).json({ error: 'Missing required information' });
            return;
        }

        // Log the quote request (in production, save to database or send email)
        console.log('Quote Request Received:', {
            timestamp: new Date().toISOString(),
            customer: customerInfo,
            package: packageDetails
        });

        // In production, you would:
        // 1. Save to database
        // 2. Send email notification to business
        // 3. Send confirmation email to customer
        // 4. Integrate with CRM system

        // For now, just return success
        res.status(200).json({
            success: true,
            message: 'Quote request submitted successfully',
            quoteId: `QUOTE-${Date.now()}`
        });

    } catch (error) {
        console.error('Quote submission error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
}
