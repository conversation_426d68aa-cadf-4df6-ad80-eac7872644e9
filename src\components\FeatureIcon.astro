---
// Feature Icon component
// Maps feature text to appropriate icons for better scanning

export interface Props {
  feature: string;
  class?: string;
  variant?: 'default' | 'small' | 'large';
}

const { 
  feature,
  class: className = '',
  variant = 'default'
} = Astro.props;

// Icon mapping based on feature keywords
const getFeatureIcon = (featureText: string): string => {
  const text = featureText.toLowerCase();
  
  // Size and quantity indicators
  if (text.includes('pumpkin') || text.includes('gourd')) return '🎃';
  if (text.includes('size') || text.includes('mini') || text.includes('large')) return '📏';
  if (text.includes('color') || text.includes('orange') || text.includes('white')) return '🎨';
  
  // Service and guarantee features
  if (text.includes('guarantee') || text.includes('week')) return '✅';
  if (text.includes('maintenance') || text.includes('visit')) return '🔧';
  if (text.includes('consultation') || text.includes('design')) return '💡';
  if (text.includes('setup') || text.includes('delivery')) return '🚚';
  
  // Coverage and space features
  if (text.includes('covers') || text.includes('sq ft') || text.includes('area')) return '📐';
  if (text.includes('entertaining') || text.includes('photo')) return '📸';
  if (text.includes('luxury') || text.includes('estate')) return '🏰';
  
  // Business and event features
  if (text.includes('commercial') || text.includes('business')) return '🏢';
  if (text.includes('brand') || text.includes('scheme')) return '🎯';
  if (text.includes('traffic') || text.includes('durability')) return '💪';
  if (text.includes('event') || text.includes('wedding')) return '🎉';
  if (text.includes('breakdown') || text.includes('pickup')) return '📦';
  if (text.includes('support') || text.includes('coordination')) return '🤝';
  if (text.includes('specialist') || text.includes('expertise')) return '👨‍💼';
  if (text.includes('seasonal') || text.includes('refresh')) return '🔄';
  
  // Default icon
  return '⭐';
};

const getVariantClasses = () => {
  switch (variant) {
    case 'small':
      return 'text-sm';
    case 'large':
      return 'text-lg';
    default:
      return 'text-base';
  }
};

const icon = getFeatureIcon(feature);
---

<span 
  class:list={[
    'inline-flex items-center justify-center w-5 h-5 text-primary-500 flex-shrink-0',
    getVariantClasses(),
    className
  ]}
  title={`Icon for: ${feature}`}
>
  {icon}
</span>

<style>
  /* Ensure consistent icon sizing */
  span {
    min-width: 1.25rem;
    min-height: 1.25rem;
  }
  
  /* Hover effect for better interactivity */
  span:hover {
    transform: scale(1.1);
    transition: transform 0.2s ease-in-out;
  }
  
  /* Responsive adjustments */
  @media (max-width: 640px) {
    .text-base {
      font-size: 0.875rem;
    }
  }
</style>
