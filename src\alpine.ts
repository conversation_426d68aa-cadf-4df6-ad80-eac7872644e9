// Alpine.js configuration and global stores
// Simplified and modular approach following SOLID principles

import type { Alpine } from 'alpinejs';
import type { Package, ShowcasePhoto } from './types';
import { createNavigationStore } from './stores/navigation';
import { createPackageStore } from './stores/packages';
import { createCarouselStore } from './stores/carousel';
import { createFormStore } from './stores/form';

// Simplified global stores using composition
export function createStores() {
  return {
    navigation: createNavigationStore(),
    packages: createPackageStore(),
    carousel: createCarouselStore(),
    form: createFormStore()
  };
}

// Simplified component factories using the modular stores
export function navigationComponent() {
  return createNavigationStore();
}

export function packageComponent() {
  return createPackageStore();
}

export function carouselComponent(photos: ShowcasePhoto[]) {
  return createCarouselStore(photos);
}

export function contactFormComponent() {
  return createFormStore();
}

// Alpine.js initialization function
export function initializeAlpine(Alpine: Alpine) {
  // Register global stores
  const stores = createStores();
  Alpine.store('navigation', stores.navigation);
  Alpine.store('packages', stores.packages);
  Alpine.store('carousel', stores.carousel);
  Alpine.store('form', stores.form);

  // Register component data functions
  Alpine.data('navigation', navigationComponent);
  Alpine.data('packageComponent', packageComponent);
  Alpine.data('carousel', carouselComponent);
  Alpine.data('contactForm', contactFormComponent);

  console.log('🏔️ Alpine.js initialized with simplified stores');
}

// Legacy contact form component - now uses simplified store
export function contactForm() {
  return createFormStore();
}

// Default export for Alpine.js initialization
export default function(Alpine: Alpine) {
  initializeAlpine(Alpine);
}
