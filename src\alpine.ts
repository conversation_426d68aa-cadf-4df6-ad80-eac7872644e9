// Alpine.js configuration and global stores
// Optimized for mobile-first performance and SOLID principles

import type { Alpine } from 'alpinejs';
import type { AppStore, Package, ShowcasePhoto, Notification } from './types';

// Global app store following single responsibility principle
export function createAppStore(): AppStore {
  return {
    // Navigation state
    mobileMenuOpen: false,

    // Package selection
    selectedPackage: null,
    packageModalOpen: false,

    // Form state
    currentStep: 1,
    formData: {},
    formErrors: {},

    // UI state
    loading: false,
    notifications: [],

    // Mobile navigation methods
    toggleMobileMenu() {
      console.log('🍔 toggleMobileMenu called, current state:', this.mobileMenuOpen);
      this.mobileMenuOpen = !this.mobileMenuOpen;
      console.log('🍔 toggleMobileMenu new state:', this.mobileMenuOpen);

      // Check if mobile menu element exists and is responding
      const mobileMenu = document.querySelector('.mobile-menu-overlay');
      console.log('🍔 Mobile menu element found:', !!mobileMenu);
      if (mobileMenu) {
        console.log('🍔 Mobile menu element:', mobileMenu);
        console.log('🍔 Mobile menu computed display:', getComputedStyle(mobileMenu).display);
        console.log('🍔 Mobile menu x-show attribute:', mobileMenu.getAttribute('x-show'));
        console.log('🍔 Mobile menu classes:', mobileMenu.className);
        console.log('🍔 Mobile menu x-cloak:', mobileMenu.hasAttribute('x-cloak'));
      } else {
        // Try alternative selectors
        const altMenu1 = document.querySelector('[x-show="$store.app.mobileMenuOpen"]');
        const altMenu2 = document.querySelector('div[x-show*="mobileMenuOpen"]');
        console.log('🍔 Alternative menu selectors:');
        console.log('🍔 [x-show="$store.app.mobileMenuOpen"] found:', !!altMenu1);
        console.log('🍔 div[x-show*="mobileMenuOpen"] found:', !!altMenu2);
        if (altMenu1) console.log('🍔 Alt menu 1:', altMenu1);
        if (altMenu2) console.log('🍔 Alt menu 2:', altMenu2);
      }

      // Prevent body scroll when menu is open (mobile UX)
      if (this.mobileMenuOpen) {
        document.body.style.overflow = 'hidden';
        console.log('🍔 Body scroll disabled');
      } else {
        document.body.style.overflow = '';
        console.log('🍔 Body scroll enabled');
      }
    },

    closeMobileMenu() {
      this.mobileMenuOpen = false;
      document.body.style.overflow = '';
    },

    // Handle smooth scrolling for mobile
    scrollToSection(sectionId: string) {
      const element = document.getElementById(sectionId);
      if (element) {
        element.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
        this.closeMobileMenu();
      }
    },

    // Global package selection method
    selectPackageInForm(packageId: string) {
      // Update the form data directly
      const formElement = document.querySelector('[x-data*="contactForm"]');
      if (formElement && (formElement as any)._x_dataStack) {
        const formData = (formElement as any)._x_dataStack[0];
        if (formData && formData.formData) {
          formData.formData.package = packageId;
        }
      }

      // Also update the DOM select element
      const packageSelect = document.getElementById('package') as HTMLSelectElement;
      if (packageSelect) {
        packageSelect.value = packageId;

        // Trigger change event
        const changeEvent = new Event('change', { bubbles: true });
        packageSelect.dispatchEvent(changeEvent);
      }

      // Scroll to contact form
      const contactSection = document.getElementById('contact');
      if (contactSection) {
        contactSection.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }
    }
  };
}

// Navigation component - mobile-first approach
export function navigationComponent() {
  return {
    mobileMenuOpen: false,
    
    toggleMobileMenu() {
      this.mobileMenuOpen = !this.mobileMenuOpen;
      
      // Prevent body scroll when menu is open (mobile UX)
      if (this.mobileMenuOpen) {
        document.body.style.overflow = 'hidden';
      } else {
        document.body.style.overflow = '';
      }
    },
    
    closeMobileMenu() {
      this.mobileMenuOpen = false;
      document.body.style.overflow = '';
    },
    
    // Handle smooth scrolling for mobile
    scrollToSection(sectionId: string) {
      const element = document.getElementById(sectionId);
      if (element) {
        element.scrollIntoView({ 
          behavior: 'smooth',
          block: 'start'
        });
        this.closeMobileMenu();
      }
    }
  };
}

// Package selection component
export function packageComponent() {
  return {
    selectedPackage: null as Package | null,
    modalOpen: false,
    packages: [] as Package[],

    init() {
      // Get packages data from the page
      const packagesScript = document.getElementById('packages-data');
      if (packagesScript) {
        try {
          this.packages = JSON.parse(packagesScript.textContent || '[]');
        } catch (e) {
          console.error('Failed to parse packages data:', e);
        }
      }
    },

    selectPackage(pkg: Package) {
      this.selectedPackage = pkg;
      this.modalOpen = true;

      // Automatically select the package in the contact form
      this.autoSelectPackageInForm(pkg.id);

      // Prevent background scroll on mobile
      document.body.style.overflow = 'hidden';
    },

    selectPackageById(packageId: string) {
      const pkg = this.packages.find(p => p.id === packageId);
      if (pkg) {
        this.selectPackage(pkg);
      }
    },

    autoSelectPackageInForm(packageId: string) {
      // Use a timeout to ensure the DOM is ready
      setTimeout(() => {
        // Find the contact form and update the package selection
        const packageSelect = document.getElementById('package') as HTMLSelectElement;
        if (packageSelect) {
          packageSelect.value = packageId;

          // Trigger change event to update Alpine.js model
          const changeEvent = new Event('change', { bubbles: true });
          packageSelect.dispatchEvent(changeEvent);

          // Also trigger input event for better compatibility
          const inputEvent = new Event('input', { bubbles: true });
          packageSelect.dispatchEvent(inputEvent);
        }

        // Update the Alpine.js form data directly if possible
        const formElement = document.querySelector('[x-data*="contactForm"]');
        if (formElement && (formElement as any)._x_dataStack) {
          const formData = (formElement as any)._x_dataStack[0];
          if (formData && formData.formData) {
            formData.formData.package = packageId;
          }
        }
      }, 100);
    },

    scrollToSection(sectionId: string) {
      const element = document.getElementById(sectionId);
      if (element) {
        element.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }
    },
    
    closeModal() {
      this.modalOpen = false;
      document.body.style.overflow = '';
    },
    
    // Mobile-optimized modal handling
    handleModalClick(event: Event) {
      if (event.target === event.currentTarget) {
        this.closeModal();
      }
    },
    
    // Handle escape key
    handleKeydown(event: KeyboardEvent) {
      if (event.key === 'Escape') {
        this.closeModal();
      }
    }
  };
}

// Photo carousel component - mobile-optimized
export function carouselComponent(photos: ShowcasePhoto[]) {
  return {
    photos,
    currentIndex: 0,
    isPlaying: true,
    isPaused: false,
    interval: null as NodeJS.Timeout | null,
    
    init() {
      this.startAutoPlay();
      
      // Handle visibility change for performance
      document.addEventListener('visibilitychange', () => {
        if (document.hidden) {
          this.pauseAutoPlay();
        } else if (this.isPlaying) {
          this.startAutoPlay();
        }
      });
    },
    
    goToSlide(index: number) {
      this.currentIndex = index;
      this.resetAutoPlay();
    },
    
    nextSlide() {
      this.currentIndex = (this.currentIndex + 1) % this.photos.length;
    },
    
    prevSlide() {
      this.currentIndex = this.currentIndex === 0 
        ? this.photos.length - 1 
        : this.currentIndex - 1;
    },
    
    startAutoPlay() {
      if (this.interval) clearInterval(this.interval);
      this.interval = setInterval(() => {
        if (!this.isPaused) {
          this.nextSlide();
        }
      }, 4000);
    },
    
    pauseAutoPlay() {
      this.isPaused = true;
    },
    
    resumeAutoPlay() {
      this.isPaused = false;
    },
    
    resetAutoPlay() {
      if (this.isPlaying) {
        this.startAutoPlay();
      }
    },
    
    // Mobile touch handling
    handleTouchStart(event: TouchEvent) {
      this.touchStartX = event.touches[0].clientX;
      this.pauseAutoPlay();
    },
    
    handleTouchEnd(event: TouchEvent) {
      if (!this.touchStartX) return;
      
      const touchEndX = event.changedTouches[0].clientX;
      const diff = this.touchStartX - touchEndX;
      
      // Minimum swipe distance
      if (Math.abs(diff) > 50) {
        if (diff > 0) {
          this.nextSlide();
        } else {
          this.prevSlide();
        }
      }
      
      this.resumeAutoPlay();
      this.touchStartX = null;
    },
    
    touchStartX: null as number | null,
  };
}

// Form validation component
export function formComponent() {
  return {
    formData: {} as Record<string, any>,
    errors: {} as Record<string, string>,
    loading: false,
    
    validateField(fieldName: string, value: string, rules: string[]) {
      const errors: string[] = [];
      
      for (const rule of rules) {
        switch (rule) {
          case 'required':
            if (!value.trim()) {
              errors.push('This field is required');
            }
            break;
          case 'email':
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (value && !emailRegex.test(value)) {
              errors.push('Please enter a valid email address');
            }
            break;
          case 'phone':
            const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
            if (value && !phoneRegex.test(value.replace(/\D/g, ''))) {
              errors.push('Please enter a valid phone number');
            }
            break;
        }
      }
      
      if (errors.length > 0) {
        this.errors[fieldName] = errors[0];
      } else {
        delete this.errors[fieldName];
      }
      
      return errors.length === 0;
    },
    
    async submitForm(endpoint: string) {
      this.loading = true;
      
      try {
        const response = await fetch(endpoint, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(this.formData),
        });
        
        if (!response.ok) {
          throw new Error('Form submission failed');
        }
        
        const result = await response.json();
        this.handleSuccess(result);
        
      } catch (error) {
        this.handleError(error as Error);
      } finally {
        this.loading = false;
      }
    },
    
    handleSuccess(result: any) {
      // Reset form
      this.formData = {};
      this.errors = {};
      
      // Show success notification
      this.$store.app.addNotification({
        type: 'success',
        title: 'Success!',
        message: 'Your request has been submitted successfully.',
      });
    },
    
    handleError(error: Error) {
      this.$store.app.addNotification({
        type: 'error',
        title: 'Error',
        message: error.message || 'Something went wrong. Please try again.',
      });
    }
  };
}

// Notification system
export function notificationStore() {
  return {
    notifications: [] as Notification[],
    
    addNotification(notification: Omit<Notification, 'id'>) {
      const id = Date.now().toString();
      const newNotification: Notification = {
        id,
        duration: 5000,
        ...notification,
      };
      
      this.notifications.push(newNotification);
      
      // Auto-remove after duration
      if (newNotification.duration) {
        setTimeout(() => {
          this.removeNotification(id);
        }, newNotification.duration);
      }
    },
    
    removeNotification(id: string) {
      this.notifications = this.notifications.filter(n => n.id !== id);
    },
    
    clearAll() {
      this.notifications = [];
    }
  };
}

// Smooth scroll utility
export function smoothScrollTo(elementId: string) {
  const element = document.getElementById(elementId);
  if (element) {
    const headerOffset = 80; // Account for fixed header
    const elementPosition = element.getBoundingClientRect().top;
    const offsetPosition = elementPosition + window.pageYOffset - headerOffset;

    window.scrollTo({
      top: offsetPosition,
      behavior: 'smooth'
    });
  }
}

// Contact form component with enhanced functionality
export function contactForm() {
  return {
    formData: {
      package: '',
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      streetAddress: '',
      city: '',
      state: '',
      zipCode: '',
      message: '',
      marketingConsent: true
    },
    errors: {},
    loading: false,
    formMode: 'order', // 'quote' or 'order'
    addressValidation: {
      isValidating: false,
      isValid: null,
      message: '',
      suggestedArea: '',
      autoDetectedArea: ''
    },

    init() {
      // Initialize toggle functionality
      this.initToggle();
      // Set initial form mode to 'order'
      this.setFormMode('order');

      // Listen for package selection events
      this.$el.addEventListener('package-selected', (event: any) => {
        const packageId = event.detail.packageId;
        if (packageId) {
          this.formData.package = packageId;
          console.log('Contact form received package selection:', packageId);

          // Auto-switch to quote mode for custom packages
          this.checkAndSwitchFormMode(packageId);
        }
      });

      // Watch for package changes via dropdown
      this.$watch('formData.package', (newPackage) => {
        if (newPackage) {
          console.log('📦 Package changed via dropdown:', newPackage);
          this.checkAndSwitchFormMode(newPackage);
        }
      });
    },

    initToggle() {
      const quoteToggle = document.getElementById('quote-toggle');
      const orderToggle = document.getElementById('order-toggle');

      if (quoteToggle && orderToggle) {
        quoteToggle.addEventListener('click', () => this.setFormMode('quote'));
        orderToggle.addEventListener('click', () => this.setFormMode('order'));
      }
    },

    setFormMode(mode: 'quote' | 'order') {
      this.formMode = mode;

      // Update toggle button states
      const quoteToggle = document.getElementById('quote-toggle');
      const orderToggle = document.getElementById('order-toggle');
      const contactTitle = document.getElementById('contact-title');
      const contactSubtitle = document.getElementById('contact-subtitle');
      const buttonText = document.getElementById('button-text');

      if (quoteToggle && orderToggle) {
        quoteToggle.classList.toggle('active', mode === 'quote');
        orderToggle.classList.toggle('active', mode === 'order');
      }

      if (contactTitle && contactSubtitle && buttonText) {
        if (mode === 'quote') {
          contactTitle.textContent = 'Get Your Free Quote';
          contactSubtitle.textContent = 'Tell us about your space and we\'ll create the perfect pumpkin display for you.';
          buttonText.textContent = 'Get Free Quote';
        } else {
          contactTitle.textContent = 'Complete Your Order';
          contactSubtitle.textContent = 'Ready to book? Complete your order with secure payment processing!';
          buttonText.textContent = 'Complete Order & Pay';
        }
      }

      // Initialize Stripe if switching to order mode
      if (mode === 'order' && typeof window.initializeStripe === 'function') {
        window.initializeStripe();
      }
    },

    validateField(fieldName: string, value: string, rules: string[]) {
      const errors = [];

      for (const rule of rules) {
        switch (rule) {
          case 'required':
            if (!value || !value.trim()) {
              errors.push('This field is required');
            }
            break;
          case 'email':
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (value && !emailRegex.test(value)) {
              errors.push('Please enter a valid email address');
            }
            break;
          case 'phone':
            const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
            const cleanPhone = value.replace(/\D/g, '');
            if (value && (!phoneRegex.test(cleanPhone) || cleanPhone.length < 10)) {
              errors.push('Please enter a valid phone number');
            }
            break;
        }
      }

      if (errors.length > 0) {
        this.errors[fieldName] = errors[0];
      } else {
        delete this.errors[fieldName];
      }

      return errors.length === 0;
    },

    async validateAddress(address: string) {
      if (!address || address.length < 10) {
        this.addressValidation = {
          isValidating: false,
          isValid: null,
          message: '',
          suggestedArea: '',
          autoDetectedArea: ''
        };
        return;
      }

      this.addressValidation.isValidating = true;
      this.addressValidation.message = 'Checking service area...';

      try {
        const response = await fetch('/api/validate-address', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ address }),
        });

        const result = await response.json();

        if (result.success && result.data) {
          const validation = result.data;

          this.addressValidation = {
            isValidating: false,
            isValid: validation.isValid,
            message: validation.message,
            suggestedArea: validation.suggestedArea || '',
            autoDetectedArea: validation.serviceArea?.id || ''
          };

          // Service area is automatically detected but not stored in form
          // The validation provides confirmation that the address is serviceable
        } else {
          this.addressValidation = {
            isValidating: false,
            isValid: false,
            message: 'Unable to verify address. Please check the format.',
            suggestedArea: '',
            autoDetectedArea: ''
          };
        }
      } catch (error) {
        console.error('Address validation error:', error);
        this.addressValidation = {
          isValidating: false,
          isValid: false,
          message: 'Unable to verify address. Please try again.',
          suggestedArea: '',
          autoDetectedArea: ''
        };
      }
    },

    validateFullAddress() {
      // Only validate if all address fields are filled
      const { streetAddress, city, state, zipCode } = this.formData;

      if (!streetAddress || !city || !state || !zipCode) {
        this.addressValidation = {
          isValid: null,
          isValidating: false,
          message: '',
          suggestedArea: '',
          autoDetectedArea: ''
        };
        return;
      }

      // Combine address fields for validation
      const fullAddress = `${streetAddress}, ${city}, ${state} ${zipCode}`;

      this.addressValidation.isValidating = true;
      this.addressValidation.message = 'Checking service area...';

      // Debounce validation
      clearTimeout(this.addressValidationTimeout);
      this.addressValidationTimeout = setTimeout(async () => {
        try {
          // First, do a quick local validation based on state and ZIP
          const isLikelyInServiceArea = this.quickServiceAreaCheck(state, zipCode);

          if (!isLikelyInServiceArea) {
            this.addressValidation = {
              isValid: false,
              isValidating: false,
              message: `We don't currently serve ${state}. We serve Utah, Southern Idaho, and Western Wyoming. You can still request a free quote - we may be able to arrange special delivery!`,
              suggestedArea: 'Utah, Southern Idaho, Western Wyoming',
              autoDetectedArea: ''
            };
            return;
          }

          // If likely in service area, do full geocoding validation
          const response = await fetch('/api/validate-address', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              address: fullAddress,
              streetAddress,
              city,
              state,
              zipCode
            }),
          });

          const result = await response.json();

          if (result.success && result.data) {
            const validation = result.data;
            this.addressValidation = {
              isValidating: false,
              isValid: validation.isValid,
              message: validation.message,
              suggestedArea: validation.suggestedArea || '',
              autoDetectedArea: validation.serviceArea?.id || ''
            };
          } else {
            this.addressValidation = {
              isValid: false,
              isValidating: false,
              message: 'Unable to verify address. Please ensure it\'s complete and try again.',
              suggestedArea: '',
              autoDetectedArea: ''
            };
          }
        } catch (error) {
          console.error('Address validation error:', error);
          this.addressValidation = {
            isValid: false,
            isValidating: false,
            message: 'Address validation temporarily unavailable. We\'ll verify during processing.',
            suggestedArea: '',
            autoDetectedArea: ''
          };
        }
      }, 800);
    },

    quickServiceAreaCheck(state: string, zipCode: string): boolean {
      // Quick check based on state and ZIP patterns
      const serviceStates = ['UT', 'ID', 'WY'];

      if (!serviceStates.includes(state)) {
        return false;
      }

      // Basic ZIP code patterns for service areas
      const serviceZipPatterns = {
        'UT': /^(84|83)/,  // Most Utah ZIP codes
        'ID': /^(83|832|833|834|835)/,  // Southern Idaho
        'WY': /^(83)/  // Western Wyoming (Jackson Hole area)
      };

      const pattern = serviceZipPatterns[state as keyof typeof serviceZipPatterns];
      return pattern ? pattern.test(zipCode) : true;
    },

    isInServiceArea(): boolean {
      const { state, zipCode } = this.formData;
      if (!state || !zipCode) return true; // Allow if not filled yet
      return this.quickServiceAreaCheck(state, zipCode);
    },

    checkAndSwitchFormMode(packageId: string) {
      // Packages that require quotes instead of direct orders
      const quoteOnlyPackages = ['business-solutions', 'event-decor', 'custom'];

      if (quoteOnlyPackages.includes(packageId)) {
        console.log('📋 Switching to quote mode for package:', packageId);
        this.setFormMode('quote');
      }
    },

    formatPhone(phone: string): string {
      const cleaned = phone.replace(/\D/g, '');
      const match = cleaned.match(/^(\d{3})(\d{3})(\d{4})$/);
      if (match) {
        return `(${match[1]}) ${match[2]}-${match[3]}`;
      }
      return phone;
    },

    handleEnterKey(event: KeyboardEvent) {
      // Don't prevent Enter on submit button or textarea
      const target = event.target as HTMLElement;
      if (target.tagName === 'BUTTON' || target.tagName === 'TEXTAREA') {
        return;
      }

      // Prevent form submission
      event.preventDefault();

      // Find all form inputs
      const form = target.closest('form');
      if (!form) return;

      const inputs = Array.from(form.querySelectorAll('input, select, textarea')).filter(
        (input) => {
          const element = input as HTMLInputElement;
          return !element.disabled && !element.hidden && element.type !== 'hidden';
        }
      ) as HTMLElement[];

      // Find current input index
      const currentIndex = inputs.indexOf(target);
      if (currentIndex === -1) return;

      // Move to next input
      const nextIndex = currentIndex + 1;
      if (nextIndex < inputs.length) {
        const nextInput = inputs[nextIndex] as HTMLInputElement;
        nextInput.focus();

        // Select text if it's a text input
        if (nextInput.type === 'text' || nextInput.type === 'email' || nextInput.type === 'tel') {
          nextInput.select();
        }
      }
    },

    async submitForm(endpoint: string) {
      // Validate all required fields
      const requiredFields = ['firstName', 'lastName', 'email', 'phone', 'streetAddress', 'city', 'state', 'zipCode'];
      let isValid = true;

      for (const field of requiredFields) {
        if (!this.validateField(field, this.formData[field], ['required'])) {
          isValid = false;
        }
      }

      // Validate email and phone specifically
      if (!this.validateField('email', this.formData.email, ['email'])) {
        isValid = false;
      }
      if (!this.validateField('phone', this.formData.phone, ['phone'])) {
        isValid = false;
      }

      if (!isValid) {
        this.$store.notifications.addNotification({
          type: 'error',
          title: 'Validation Error',
          message: 'Please fix the errors above and try again.'
        });
        return;
      }

      // Check service area for orders (but allow quotes anywhere)
      if (this.formMode === 'order' && !this.isInServiceArea()) {
        this.$store.notifications.addNotification({
          type: 'error',
          title: 'Service Area Restriction',
          message: 'We currently only accept orders in Utah, Southern Idaho, and Western Wyoming. You can still request a free quote - we may be able to arrange special delivery!'
        });
        return;
      }

      this.loading = true;

      try {
        // Choose endpoint based on form mode
        const submitEndpoint = this.formMode === 'order' ? '/api/create-order' : '/api/submit-quote';

        const response = await fetch(submitEndpoint, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            formData: this.formData,
            formMode: this.formMode
          }),
        });

        const result = await response.json();

        if (!response.ok) {
          throw new Error(result.message || 'Form submission failed');
        }

        this.handleSuccess(result);

      } catch (error) {
        this.handleError(error as Error);
      } finally {
        this.loading = false;
      }
    },

    handleSuccess(result: any) {
      // Reset form
      this.formData = {
        package: '',
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        streetAddress: '',
        city: '',
        state: '',
        zipCode: '',
        message: '',
        marketingConsent: true
      };
      this.errors = {};

      // Show success notification
      this.$store.notifications.addNotification({
        type: 'success',
        title: 'Quote Request Sent!',
        message: `Thank you! We'll contact you within ${result.data?.estimatedResponse || '24 hours'} with your personalized quote.`,
        duration: 8000
      });

      // Scroll to top
      window.scrollTo({ top: 0, behavior: 'smooth' });
    },

    handleError(error: Error) {
      console.error('Form submission error:', error);
      this.$store.notifications.addNotification({
        type: 'error',
        title: 'Submission Failed',
        message: error.message || 'Something went wrong. Please try again or call us directly at (*************.',
        duration: 10000
      });
    }
  };
}

// Initialize Alpine.js with our components
export default function(Alpine: Alpine) {
  // Register global stores
  Alpine.store('app', createAppStore());
  Alpine.store('notifications', notificationStore());

  // Register global components
  Alpine.data('navigation', navigationComponent);
  Alpine.data('packageSelector', packageComponent);
  Alpine.data('carousel', carouselComponent);
  Alpine.data('contactForm', contactForm);

  // Global utilities
  Alpine.magic('formatPrice', () => (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
    }).format(price);
  });

  Alpine.magic('formatPhone', () => (phone: string) => {
    const cleaned = phone.replace(/\D/g, '');
    const match = cleaned.match(/^(\d{3})(\d{3})(\d{4})$/);
    if (match) {
      return `(${match[1]}) ${match[2]}-${match[3]}`;
    }
    return phone;
  });

  Alpine.magic('scrollTo', () => (elementId: string) => {
    smoothScrollTo(elementId);
  });

  // Global directives for enhanced functionality
  Alpine.directive('scroll-to', (el, { expression }, { evaluate }) => {
    el.addEventListener('click', (e) => {
      e.preventDefault();
      const target = evaluate(expression);
      smoothScrollTo(target);
    });
  });

  // Global function for package selection
  (window as any).selectPackageAndScroll = (packageId: string) => {
    console.log('🎃 Selecting package:', packageId);

    // Update the form data using Alpine's reactive system
    setTimeout(() => {
      // Approach 0: Try using Alpine's global store if available
      if ((window as any).Alpine && (window as any).Alpine.store) {
        try {
          const appStore = (window as any).Alpine.store('app');
          if (appStore && appStore.selectPackageInForm) {
            console.log('🏪 Using Alpine store method');
            appStore.selectPackageInForm(packageId);
          }
        } catch (e) {
          console.log('🏪 Alpine store method not available:', e);
        }
      }

      // Approach 1: Update DOM select element first
      const packageSelect = document.getElementById('package') as HTMLSelectElement;
      if (packageSelect) {
        console.log('📦 Found package select element, current value:', packageSelect.value);
        packageSelect.value = packageId;
        console.log('📦 Updated select element value to:', packageSelect.value);

        // Trigger multiple events for better compatibility
        const changeEvent = new Event('change', { bubbles: true });
        const inputEvent = new Event('input', { bubbles: true });
        packageSelect.dispatchEvent(changeEvent);
        packageSelect.dispatchEvent(inputEvent);
        console.log('📦 Triggered change and input events');
      } else {
        console.error('❌ Package select element not found!');
      }

      // Approach 2: Try multiple selectors to find the Alpine.js form
      let formElement = document.querySelector('form[x-data*="contactForm"]');
      if (!formElement) {
        formElement = document.querySelector('form[x-data]');
        console.log('📋 Trying fallback form selector');
      }
      if (!formElement) {
        formElement = document.querySelector('#contact form');
        console.log('📋 Trying contact section form selector');
      }

      if (formElement) {
        console.log('📋 Found contact form element:', formElement);
        console.log('📋 Form x-data attribute:', formElement.getAttribute('x-data'));

        // Use Alpine's $data to access the component data
        const alpineData = (formElement as any)._x_dataStack?.[0];
        console.log('📋 Alpine data stack:', (formElement as any)._x_dataStack);

        if (alpineData && alpineData.formData) {
          console.log('📋 Current Alpine form data package:', alpineData.formData.package);
          alpineData.formData.package = packageId;
          console.log('📋 Updated Alpine.js form data to:', alpineData.formData.package);

          // Check if we need to switch form mode
          if (alpineData.checkAndSwitchFormMode) {
            alpineData.checkAndSwitchFormMode(packageId);
          }

          // Force Alpine to update the DOM
          if (typeof (formElement as any)._x_updateElements === 'function') {
            (formElement as any)._x_updateElements();
          }
        } else {
          console.error('❌ Alpine data not found on form element. Available keys:', Object.keys(alpineData || {}));
        }

        // Also dispatch custom event
        formElement.dispatchEvent(new CustomEvent('package-selected', {
          detail: { packageId },
          bubbles: true
        }));
        console.log('📋 Dispatched package-selected event');
      } else {
        console.error('❌ Contact form element not found with any selector!');
      }

      // Approach 3: Final fallback - manually set the select value and force Alpine update
      setTimeout(() => {
        const packageSelect = document.getElementById('package') as HTMLSelectElement;
        if (packageSelect && packageSelect.value !== packageId) {
          console.log('🔄 Final fallback: forcing select value update');
          packageSelect.value = packageId;

          // Try to trigger Alpine's x-model update manually
          if ((window as any).Alpine) {
            try {
              (window as any).Alpine.nextTick(() => {
                const event = new Event('change', { bubbles: true });
                packageSelect.dispatchEvent(event);
                console.log('🔄 Triggered Alpine nextTick update');
              });
            } catch (e) {
              console.log('🔄 Alpine nextTick not available:', e);
            }
          }
        }
      }, 500);

      // Scroll to contact form
      const contactSection = document.getElementById('contact');
      if (contactSection) {
        contactSection.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
        console.log('📍 Scrolled to contact section');
      } else {
        console.error('❌ Contact section not found!');
      }
    }, 200);
  };
}
