// Quote submission API endpoint
import type { APIRoute } from 'astro';
import { validateAndSanitizeForm } from '../../utils/validation';
import { createSuccessResponse, createErrorResponse, checkRateLimit, safeParseJson } from '../../utils/api';

export const POST: APIRoute = async ({ request, clientAddress }) => {
  try {
    // Rate limiting
    const rateLimitResult = await checkRateLimit(clientAddress, 'quote-submission', 5, 300); // 5 requests per 5 minutes
    if (!rateLimitResult.allowed) {
      return createErrorResponse('Too many quote requests. Please try again later.', 429);
    }

    // Parse and validate request
    const parseResult = await safeParseJson(request);
    if (!parseResult.success) {
      return createErrorResponse('Invalid request format', 400);
    }

    const { formData, formMode } = parseResult.data;

    // Validate form mode
    if (formMode !== 'quote') {
      return createErrorResponse('Invalid form mode for quote submission', 400);
    }

    // Validate and sanitize form data
    const validationResult = validateAndSanitizeForm(formData);
    if (!validationResult.isValid) {
      return createErrorResponse('Validation failed', 400, {
        validationErrors: validationResult.errors
      });
    }

    // Log the quote request (in production, save to database or send email)
    console.log('Quote Request Received:', {
      timestamp: new Date().toISOString(),
      customer: {
        name: `${validationResult.sanitizedData.firstName} ${validationResult.sanitizedData.lastName}`,
        email: validationResult.sanitizedData.email,
        phone: validationResult.sanitizedData.phone,
        address: `${validationResult.sanitizedData.streetAddress}, ${validationResult.sanitizedData.city}, ${validationResult.sanitizedData.state} ${validationResult.sanitizedData.zipCode}`
      },
      package: validationResult.sanitizedData.package,
      message: validationResult.sanitizedData.message,
      marketingConsent: validationResult.sanitizedData.marketingConsent
    });

    // Generate quote ID
    const quoteId = `QUOTE-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    // In production, you would:
    // 1. Save to database
    // 2. Send email notification to business
    // 3. Send confirmation email to customer
    // 4. Integrate with CRM system

    return createSuccessResponse({
      message: 'Quote request submitted successfully',
      quoteId,
      estimatedResponse: '24 hours'
    });

  } catch (error) {
    console.error('Quote submission error:', error);
    return createErrorResponse('Internal server error', 500);
  }
};
