// Quote submission API endpoint
import type { APIRoute } from 'astro';

export const POST: APIRoute = async ({ request }) => {
  try {
    const body = await request.json();
    const { formData, formMode } = body;

    // Validate required fields
    if (!formData || formMode !== 'quote') {
      return new Response(JSON.stringify({ 
        success: false, 
        error: 'Invalid quote data' 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Validate required fields
    const requiredFields = ['firstName', 'lastName', 'email', 'phone', 'streetAddress', 'city', 'state', 'zipCode'];
    for (const field of requiredFields) {
      if (!formData[field] || !formData[field].toString().trim()) {
        return new Response(JSON.stringify({
          success: false,
          error: `Missing required field: ${field}`
        }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        });
      }
    }

    // Log the quote request (in production, save to database or send email)
    console.log('Quote Request Received:', {
      timestamp: new Date().toISOString(),
      customer: {
        name: `${formData.firstName} ${formData.lastName}`,
        email: formData.email,
        phone: formData.phone,
        address: `${formData.streetAddress}, ${formData.city}, ${formData.state} ${formData.zipCode}`
      },
      package: formData.package,
      message: formData.message,
      marketingConsent: formData.marketingConsent
    });

    // In production, you would:
    // 1. Save to database
    // 2. Send email notification to business
    // 3. Send confirmation email to customer
    // 4. Integrate with CRM system

    return new Response(JSON.stringify({
      success: true,
      message: 'Quote request submitted successfully',
      quoteId: `QUOTE-${Date.now()}`
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Quote submission error:', error);
    return new Response(JSON.stringify({ 
      success: false, 
      error: 'Internal server error' 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
