// Enhanced Lazy Loading System for Mountain Porch Pumpkins
// Implements intersection observer for images, content sections, and components

interface LazyLoadOptions {
  rootMargin?: string;
  threshold?: number;
  once?: boolean;
}

class LazyLoader {
  private imageObserver: IntersectionObserver | null = null;
  private contentObserver: IntersectionObserver | null = null;
  private componentObserver: IntersectionObserver | null = null;

  constructor() {
    this.init();
  }

  private init() {
    if (!('IntersectionObserver' in window)) {
      // Fallback for browsers without IntersectionObserver
      this.loadAllContent();
      return;
    }

    this.setupImageObserver();
    this.setupContentObserver();
    this.setupComponentObserver();
  }

  // Enhanced image lazy loading
  private setupImageObserver() {
    this.imageObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          this.loadImage(entry.target as HTMLImageElement);
          this.imageObserver?.unobserve(entry.target);
        }
      });
    }, {
      rootMargin: '50px 0px', // Load images 50px before they enter viewport
      threshold: 0.01
    });

    // Observe all lazy images
    this.observeImages();
  }

  // Content section lazy loading (for animations and heavy content)
  private setupContentObserver() {
    this.contentObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          this.loadContent(entry.target as HTMLElement);
          this.contentObserver?.unobserve(entry.target);
        }
      });
    }, {
      rootMargin: '100px 0px', // Load content 100px before viewport
      threshold: 0.1
    });

    // Observe all lazy content sections
    this.observeContent();
  }

  // Component lazy loading (for complex components like carousels)
  private setupComponentObserver() {
    this.componentObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          this.loadComponent(entry.target as HTMLElement);
          this.componentObserver?.unobserve(entry.target);
        }
      });
    }, {
      rootMargin: '200px 0px', // Load components 200px before viewport
      threshold: 0.05
    });

    // Observe all lazy components
    this.observeComponents();
  }

  private loadImage(img: HTMLImageElement) {
    // Load main image source
    if (img.dataset.src) {
      img.src = img.dataset.src;
      img.removeAttribute('data-src');
    }

    // Load responsive srcset
    if (img.dataset.srcset) {
      img.srcset = img.dataset.srcset;
      img.removeAttribute('data-srcset');
    }

    // Add loaded class for any CSS transitions
    img.classList.add('lazy-loaded');

    // Dispatch custom event
    img.dispatchEvent(new CustomEvent('lazyloaded', { bubbles: true }));
  }

  private loadContent(element: HTMLElement) {
    // Remove lazy loading class
    element.classList.remove('lazy-content');
    element.classList.add('content-loaded');

    // Load any deferred content
    const deferredContent = element.querySelector('[data-deferred-content]');
    if (deferredContent) {
      const content = deferredContent.getAttribute('data-deferred-content');
      if (content) {
        deferredContent.innerHTML = content;
        deferredContent.removeAttribute('data-deferred-content');
      }
    }

    // Trigger any animations
    element.classList.add('animate-in');

    // Dispatch custom event
    element.dispatchEvent(new CustomEvent('contentloaded', { bubbles: true }));
  }

  private loadComponent(element: HTMLElement) {
    // Remove lazy loading class
    element.classList.remove('lazy-component');
    element.classList.add('component-loaded');

    // Initialize component-specific functionality
    const componentType = element.dataset.component;
    
    switch (componentType) {
      case 'carousel':
        this.initializeCarousel(element);
        break;
      case 'gallery':
        this.initializeGallery(element);
        break;
      case 'form':
        this.initializeForm(element);
        break;
      default:
        // Generic component initialization
        break;
    }

    // Dispatch custom event
    element.dispatchEvent(new CustomEvent('componentloaded', { bubbles: true }));
  }

  private initializeCarousel(element: HTMLElement) {
    // Initialize carousel functionality when it becomes visible
    const images = element.querySelectorAll('img[data-src]');
    images.forEach(img => {
      if (this.imageObserver) {
        this.imageObserver.observe(img);
      }
    });
  }

  private initializeGallery(element: HTMLElement) {
    // Initialize gallery functionality when it becomes visible
    const images = element.querySelectorAll('img[data-src]');
    images.forEach(img => {
      if (this.imageObserver) {
        this.imageObserver.observe(img);
      }
    });
  }

  private initializeForm(element: HTMLElement) {
    // Initialize form functionality when it becomes visible
    // This could include loading form validation scripts, etc.
  }

  private observeImages() {
    const lazyImages = document.querySelectorAll('img[data-src], img[loading="lazy"]');
    lazyImages.forEach(img => {
      if (this.imageObserver) {
        this.imageObserver.observe(img);
      }
    });
  }

  private observeContent() {
    const lazyContent = document.querySelectorAll('.lazy-content');
    lazyContent.forEach(content => {
      if (this.contentObserver) {
        this.contentObserver.observe(content);
      }
    });
  }

  private observeComponents() {
    const lazyComponents = document.querySelectorAll('.lazy-component');
    lazyComponents.forEach(component => {
      if (this.componentObserver) {
        this.componentObserver.observe(component);
      }
    });
  }

  // Fallback for browsers without IntersectionObserver
  private loadAllContent() {
    // Load all images immediately
    const lazyImages = document.querySelectorAll('img[data-src]');
    lazyImages.forEach(img => this.loadImage(img as HTMLImageElement));

    // Load all content
    const lazyContent = document.querySelectorAll('.lazy-content');
    lazyContent.forEach(content => this.loadContent(content as HTMLElement));

    // Load all components
    const lazyComponents = document.querySelectorAll('.lazy-component');
    lazyComponents.forEach(component => this.loadComponent(component as HTMLElement));
  }

  // Public method to manually trigger loading
  public loadElement(element: HTMLElement) {
    if (element.tagName === 'IMG') {
      this.loadImage(element as HTMLImageElement);
    } else if (element.classList.contains('lazy-content')) {
      this.loadContent(element);
    } else if (element.classList.contains('lazy-component')) {
      this.loadComponent(element);
    }
  }

  // Public method to add new elements to observation
  public observe(element: HTMLElement, type: 'image' | 'content' | 'component' = 'content') {
    switch (type) {
      case 'image':
        this.imageObserver?.observe(element);
        break;
      case 'content':
        this.contentObserver?.observe(element);
        break;
      case 'component':
        this.componentObserver?.observe(element);
        break;
    }
  }

  // Cleanup method
  public destroy() {
    this.imageObserver?.disconnect();
    this.contentObserver?.disconnect();
    this.componentObserver?.disconnect();
  }
}

// Initialize lazy loading when DOM is ready
let lazyLoader: LazyLoader;

if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    lazyLoader = new LazyLoader();
  });
} else {
  lazyLoader = new LazyLoader();
}

// Export for use in other modules
export { LazyLoader };

// Make available globally for inline scripts
(window as any).LazyLoader = LazyLoader;
