---
// Base layout component following SOLID principles
// Single Responsibility: Handles page structure and meta tags
// Open/Closed: Extensible through props and slots
// Interface Segregation: Clean prop interface

import PerformanceMonitor from '../components/PerformanceMonitor.astro';
import '../styles/global.css';

export interface Props {
  title: string;
  description: string;
  image?: string;
  canonical?: string;
  noindex?: boolean;
  type?: 'website' | 'article' | 'product';
  structuredData?: Record<string, any>;
}

const {
  title,
  description,
  image = '/images/MPP-Hero.webp',
  canonical,
  noindex = false,
  type = 'website',
  structuredData
} = Astro.props;

// Generate canonical URL
const canonicalURL = canonical || new URL(Astro.url.pathname, Astro.site);

// Default structured data for local business
const defaultStructuredData = {
  "@context": "https://schema.org",
  "@type": "LocalBusiness",
  "name": "Mountain Porch Pumpkins",
  "description": "Professional pumpkin delivery and display service bringing autumn magic to your doorstep. Skip the pumpkin patch hassle - more time with family, less time driving to farms.",
  "url": "https://mountainporchpumpkins.com",
  "telephone": "******-555-0123",
  "email": "<EMAIL>",
  "address": {
    "@type": "PostalAddress",
    "addressLocality": "Riverdale",
    "addressRegion": "UT",
    "addressCountry": "US"
  },
  "areaServed": [
    { "@type": "State", "name": "Utah" },
    { "@type": "State", "name": "Idaho" },
    { "@type": "State", "name": "Wyoming" }
  ],
  "priceRange": "$349-$999",
  "openingHours": "Mo-Su 08:00-18:00",
  "foundingDate": "2020",
  "serviceArea": {
    "@type": "GeoCircle",
    "geoMidpoint": {
      "@type": "GeoCoordinates",
      "latitude": "41.1732",
      "longitude": "-111.9391"
    },
    "geoRadius": "500000"
  },
  "hasOfferCatalog": {
    "@type": "OfferCatalog",
    "name": "Pumpkin Display Packages",
    "itemListElement": [
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Harvest Essentials Package",
          "description": "Perfect for apartments and small porches"
        },
        "price": "349",
        "priceCurrency": "USD"
      },
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Pumpkin Charm Package",
          "description": "Ideal for small to medium porches"
        },
        "price": "599",
        "priceCurrency": "USD"
      },
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Fall Favorites Package",
          "description": "Our most popular package for medium to large porches"
        },
        "price": "799",
        "priceCurrency": "USD"
      },
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Autumn Splendor Package",
          "description": "Grand displays for large spaces and luxury properties"
        },
        "price": "999",
        "priceCurrency": "USD"
      }
    ]
  }
};

const finalStructuredData = structuredData || defaultStructuredData;
---

<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
  <!-- Essential meta tags -->
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  
  <!-- SEO meta tags -->
  <title>{title}</title>
  <meta name="description" content={description}>
  <meta name="keywords" content="pumpkin delivery Utah, fall decorations Salt Lake City, autumn displays Jackson Hole, pumpkin delivery Idaho Wyoming, professional pumpkin setup, pumpkin patch alternative, seasonal decor Park City, fall display service Boise, autumn decorations Sun Valley, pumpkin arrangement Moab, Mountain Porch Pumpkins">
  <meta name="author" content="Mountain Porch Pumpkins">
  <meta name="geo.region" content="US-UT">
  <meta name="geo.placename" content="Riverdale, Utah">
  <meta name="geo.position" content="41.1732;-111.9391">
  <meta name="ICBM" content="41.1732, -111.9391">
  <link rel="canonical" href={canonicalURL}>
  {noindex && <meta name="robots" content="noindex, nofollow">}
  
  <!-- Open Graph / Facebook -->
  <meta property="og:type" content={type}>
  <meta property="og:url" content={canonicalURL}>
  <meta property="og:title" content={title}>
  <meta property="og:description" content={description}>
  <meta property="og:image" content={new URL(image, Astro.site)}>
  <meta property="og:image:width" content="1200">
  <meta property="og:image:height" content="630">
  <meta property="og:site_name" content="Mountain Porch Pumpkins">
  <meta property="og:locale" content="en_US">
  
  <!-- Twitter Card -->
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:url" content={canonicalURL}>
  <meta name="twitter:title" content={title}>
  <meta name="twitter:description" content={description}>
  <meta name="twitter:image" content={new URL(image, Astro.site)}>
  
  <!-- Favicon -->
  <link rel="icon" type="image/svg+xml" href="/favicon.svg">
  <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32.png">
  <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16.png">
  <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
  
  <!-- PWA Manifest -->
  <link rel="manifest" href="/manifest.json">
  
  <!-- Performance optimizations -->
  <meta name="theme-color" content="#BF9264">
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
  
  <!-- Preconnect to external domains -->
  <link rel="preconnect" href="https://fonts.googleapis.com" crossorigin>
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  
  <!-- Critical fonts preloaded -->
  <link 
    rel="preload" 
    href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700;800&display=swap" 
    as="style" 
    onload="this.onload=null;this.rel='stylesheet'"
  >
  <noscript>
    <link 
      href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700;800&display=swap" 
      rel="stylesheet"
    >
  </noscript>
  
  <!-- Structured Data -->
  <script type="application/ld+json" set:html={JSON.stringify(finalStructuredData)}></script>

  <!-- Critical CSS inlined by Astro -->
  <style>
    /* Critical above-the-fold styles */
    body {
      font-family: 'Poppins', system-ui, sans-serif;
      line-height: 1.6;
      margin: 0;
      padding: 0;
    }
    
    /* Prevent layout shift */
    img {
      max-width: 100%;
      height: auto;
      display: block;
    }
    
    /* Loading state */
    .loading {
      opacity: 0.7;
      pointer-events: none;
    }
    
    /* Skip link for accessibility */
    .skip-link {
      position: absolute;
      top: -40px;
      left: 6px;
      background: #ff6b35;
      color: white;
      padding: 8px;
      text-decoration: none;
      border-radius: 4px;
      z-index: 10000;
    }
    
    .skip-link:focus {
      top: 6px;
    }
  </style>
</head>

<body class="bg-gray-50 text-gray-900 antialiased">
  <!-- Skip link for accessibility -->
  <a href="#main-content" class="skip-link">Skip to main content</a>
  
  <!-- Main content wrapper -->
  <div id="app" class="min-h-screen flex flex-col">
    <!-- Header slot -->
    <slot name="header" />
    
    <!-- Main content -->
    <main id="main-content" class="flex-1">
      <slot />
    </main>
    
    <!-- Footer slot -->
    <slot name="footer" />
  </div>
  
  <!-- Notification container -->
  <div 
    id="notifications" 
    class="fixed top-4 right-4 z-50 space-y-2"
    x-data="$store.notifications"
    x-show="notifications.length > 0"
    x-transition
  >
    <template x-for="notification in notifications" :key="notification.id">
      <div 
        class="bg-white rounded-lg shadow-lg border-l-4 p-4 max-w-sm"
        :class="{
          'border-green-500': notification.type === 'success',
          'border-red-500': notification.type === 'error',
          'border-yellow-500': notification.type === 'warning',
          'border-blue-500': notification.type === 'info'
        }"
        x-show="true"
        x-transition:enter="transform ease-out duration-300"
        x-transition:enter-start="translate-x-full opacity-0"
        x-transition:enter-end="translate-x-0 opacity-100"
        x-transition:leave="transform ease-in duration-200"
        x-transition:leave-start="translate-x-0 opacity-100"
        x-transition:leave-end="translate-x-full opacity-0"
      >
        <div class="flex items-start">
          <div class="flex-1">
            <h4 class="font-medium text-gray-900" x-text="notification.title"></h4>
            <p class="text-sm text-gray-600 mt-1" x-text="notification.message"></p>
          </div>
          <button 
            @click="removeNotification(notification.id)"
            class="ml-4 text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600"
          >
            <span class="sr-only">Close</span>
            <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </button>
        </div>
      </div>
    </template>
  </div>
  
  <!-- Performance Monitoring -->
  <PerformanceMonitor enabled={import.meta.env.PROD} debug={import.meta.env.DEV} />

  <!-- Lazy Loading System -->
  <script type="module" src="/src/scripts/lazyLoading.ts"></script>

  <!-- Service Worker Registration -->
  <script>
    // Register service worker for caching and offline functionality
    if ('serviceWorker' in navigator && !import.meta.env.DEV) {
      window.addEventListener('load', async () => {
        try {
          const registration = await navigator.serviceWorker.register('/sw.js');
          console.log('✅ Service Worker registered:', registration);

          // Handle updates
          registration.addEventListener('updatefound', () => {
            const newWorker = registration.installing;
            if (newWorker) {
              newWorker.addEventListener('statechange', () => {
                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                  // New content available, show update notification
                  if (confirm('New content available! Reload to update?')) {
                    window.location.reload();
                  }
                }
              });
            }
          });

        } catch (error) {
          console.error('❌ Service Worker registration failed:', error);
        }
      });
    }

    // Initialize analytics tracking
    import('../scripts/analytics.ts').then(module => {
      console.log('📊 Analytics initialized');
    }).catch(error => {
      console.error('❌ Analytics initialization failed:', error);
    });

    // Initialize A/B testing framework
    import('../scripts/ab-testing.ts').then(module => {
      console.log('🧪 A/B Testing initialized');
    }).catch(error => {
      console.error('❌ A/B Testing initialization failed:', error);
    });

    // Initialize dynamic content system
    import('../scripts/dynamic-content.ts').then(module => {
      console.log('🎯 Dynamic Content initialized');
    }).catch(error => {
      console.error('❌ Dynamic Content initialization failed:', error);
    });
  </script>
</body>
</html>
