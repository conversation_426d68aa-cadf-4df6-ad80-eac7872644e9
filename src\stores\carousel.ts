// Carousel store - Single Responsibility Principle
// Handles photo carousel state and interactions

import type { ShowcasePhoto } from '../types';

export interface CarouselStore {
  currentIndex: number;
  isPlaying: boolean;
  photos: ShowcasePhoto[];
  nextPhoto(): void;
  previousPhoto(): void;
  goToPhoto(index: number): void;
  togglePlayback(): void;
  pauseCarousel(): void;
  resumeCarousel(): void;
}

export function createCarouselStore(photos: ShowcasePhoto[] = []): CarouselStore {
  return {
    currentIndex: 0,
    isPlaying: true,
    photos,

    nextPhoto() {
      if (this.photos.length === 0) return;
      this.currentIndex = (this.currentIndex + 1) % this.photos.length;
    },

    previousPhoto() {
      if (this.photos.length === 0) return;
      this.currentIndex = this.currentIndex === 0 
        ? this.photos.length - 1 
        : this.currentIndex - 1;
    },

    goToPhoto(index: number) {
      if (index >= 0 && index < this.photos.length) {
        this.currentIndex = index;
      }
    },

    togglePlayback() {
      this.isPlaying = !this.isPlaying;
    },

    pauseCarousel() {
      this.isPlaying = false;
    },

    resumeCarousel() {
      this.isPlaying = true;
    }
  };
}
