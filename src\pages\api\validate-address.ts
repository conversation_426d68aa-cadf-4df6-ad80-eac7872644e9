import type { APIRoute } from 'astro';
import { validateServiceArea } from '../../services/serviceAreaCalculator';
import { createSuccessResponse, createErrorResponse, checkRateLimit, safeParseJson } from '../../utils/api';
import { sanitizeInput } from '../../utils/validation';

export const prerender = false;

export const POST: APIRoute = async ({ request, clientAddress }) => {
  try {
    // Rate limiting for address validation
    const rateLimitResult = await checkRateLimit(clientAddress, 'address-validation', 20, 60); // 20 requests per minute
    if (!rateLimitResult.allowed) {
      return createErrorResponse('Too many address validation requests. Please try again later.', 429);
    }

    // Parse and validate request
    const parseResult = await safeParseJson(request);
    if (!parseResult.success) {
      return createErrorResponse('Invalid request format', 400);
    }

    const { address } = parseResult.data;

    // Validate address input
    if (!address || typeof address !== 'string' || !address.trim()) {
      return createErrorResponse('Valid address is required', 400);
    }

    // Sanitize address input
    const sanitizedAddress = sanitizeInput(address.trim());
    if (sanitizedAddress.length < 5) {
      return createErrorResponse('Address too short for validation', 400);
    }

    // Validate the service area
    const result = await validateServiceArea(sanitizedAddress);

    return createSuccessResponse(result);

  } catch (error) {
    console.error('Address validation error:', error);
    return createErrorResponse('Address validation service temporarily unavailable', 500);
  }
};
