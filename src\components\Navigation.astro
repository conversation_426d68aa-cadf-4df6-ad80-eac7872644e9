---
// Navigation component following SOLID principles
// Single Responsibility: Handles navigation UI and mobile menu
// Open/Closed: Extensible through props
// Interface Segregation: Clean prop interface

export interface Props {
  currentPath?: string;
  showCTA?: boolean;
  ctaText?: string;
  ctaLink?: string;
}

const {
  currentPath = '/',
  showCTA = true,
  ctaText = 'Get Free Quote',
  ctaLink = '#contact'
} = Astro.props;

// Navigation items - easy to extend
const navItems = [
  { href: '#home', label: 'Home', icon: '🏠' },
  { href: '#packages', label: 'Packages', icon: '🎃' },
  { href: '#about', label: 'About', icon: '👋' },
  { href: '#contact', label: 'Contact', icon: '📞' }
];

const secondaryItems = [
  { href: '/business', label: 'Business Services', icon: '🏢' },
  { href: '#contact', label: 'Custom Displays', icon: '✨' }
];
---

<header class="fixed top-0 w-full bg-black/95 backdrop-blur-md z-40 border-b border-primary-500/10">
  <nav class="container mx-auto px-4 py-4" x-data="{ mobileMenuOpen: false }">
    <div class="flex items-center justify-between">
      <!-- Brand Section -->
      <div class="flex items-center space-x-3">
        <img
          src="/images/MPPLogo.png"
          alt="Mountain Porch Pumpkins Logo"
          class="h-10 w-auto filter drop-shadow-md"
          loading="eager"
        >
        <span class="text-white font-serif font-bold text-lg hidden sm:block text-shadow">
          Mountain Porch Pumpkins
        </span>
      </div>
      
      <!-- Mobile Menu Button -->
      <button
        type="button"
        @click="mobileMenuOpen = !mobileMenuOpen"
        :aria-expanded="mobileMenuOpen"
        class="md:hidden text-white p-3 tap-target rounded-lg hover:bg-white/10 transition-colors relative z-50 min-w-[44px] min-h-[44px] flex items-center justify-center"
        aria-label="Toggle navigation menu"
        style="touch-action: manipulation;"
      >
        <svg
          class="h-6 w-6 transition-transform duration-200 pointer-events-none"
          :class="{ 'rotate-90': mobileMenuOpen }"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            x-show="!mobileMenuOpen"
            d="M4 6h16M4 12h16M4 18h16"
          />
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            x-show="mobileMenuOpen"
            d="M6 18L18 6M6 6l12 12"
          />
        </svg>
      </button>
      
      <!-- Desktop Navigation -->
      <div class="hidden md:flex items-center space-x-8">
        {navItems.map(item => (
          <a 
            href={item.href} 
            class="text-white hover:text-primary-400 transition-colors font-medium"
            class:list={[
              currentPath === item.href && 'text-primary-400'
            ]}
          >
            {item.label}
          </a>
        ))}
        {showCTA && (
          <a 
            href={ctaLink} 
            class="bg-primary-500 hover:bg-primary-600 text-white px-6 py-2 rounded-lg font-medium transition-all transform hover:scale-105 tap-target"
          >
            {ctaText}
          </a>
        )}
      </div>
    </div>
    
    <!-- Mobile Menu Overlay -->
    <div
      x-show="mobileMenuOpen"
      x-transition:enter="transition ease-out duration-300"
      x-transition:enter-start="opacity-0 transform -translate-y-full"
      x-transition:enter-end="opacity-100 transform translate-y-0"
      x-transition:leave="transition ease-in duration-200"
      x-transition:leave-start="opacity-100 transform translate-y-0"
      x-transition:leave-end="opacity-0 transform -translate-y-full"
      class="md:hidden fixed inset-x-0 top-[88px] bg-black/95 backdrop-blur-lg border-t border-primary-500/20 shadow-lg z-40"
      @click.away="mobileMenuOpen = false"
      @keydown.escape="mobileMenuOpen = false"
    >
      <div class="container mx-auto px-4 py-6 max-h-screen overflow-y-auto">
        <!-- Primary Navigation -->
        <div class="space-y-2 mb-8">
          {navItems.map(item => (
            <a
              href={item.href}
              @click="mobileMenuOpen = false"
              class="flex items-center space-x-4 p-4 text-white hover:bg-primary-500/20 hover:text-primary-300 rounded-xl transition-all tap-target"
              class:list={[
                currentPath === item.href && 'bg-primary-500/20 text-primary-300'
              ]}
            >
              <span class="text-2xl">{item.icon}</span>
              <span class="font-medium text-lg">{item.label}</span>
            </a>
          ))}
        </div>
        
        <!-- Secondary Navigation -->
        <div class="border-t border-primary-500/20 pt-6 mb-8">
          <h4 class="text-primary-400 font-semibold text-sm uppercase tracking-wider mb-4 px-4">
            Services
          </h4>
          <div class="space-y-2">
            {secondaryItems.map(item => (
              <a 
                href={item.href}
                class="flex items-center space-x-4 p-4 text-gray-300 hover:bg-primary-500/10 hover:text-white rounded-xl transition-all tap-target"
              >
                <span class="text-xl">{item.icon}</span>
                <span class="font-medium">{item.label}</span>
              </a>
            ))}
          </div>
        </div>
        
        <!-- Mobile CTA -->
        {showCTA && (
          <div class="border-t border-primary-500/20 pt-6">
            <a
              href={ctaLink}
              @click="mobileMenuOpen = false"
              class="flex items-center justify-center w-full bg-gradient-to-r from-primary-500 to-secondary-500 text-white py-4 rounded-xl font-semibold text-lg shadow-lg tap-target"
            >
              {ctaText}
            </a>
            <p class="text-center text-primary-200 text-sm mt-3">
              Setup begins September 21st
            </p>
          </div>
        )}
      </div>
    </div>
  </nav>
</header>

<style>
  /* Ensure mobile menu button is clickable */
  button[aria-label="Toggle navigation menu"] {
    position: relative;
    z-index: 9999;
    cursor: pointer;
    -webkit-tap-highlight-color: rgba(255, 255, 255, 0.1);
  }

  /* Ensure button has proper touch target */
  @media (max-width: 768px) {
    button[aria-label="Toggle navigation menu"] {
      min-width: 44px;
      min-height: 44px;
    }
  }
</style>

<script>
  console.log('🔧 Navigation component loaded - using simplified Alpine.js approach');
</script>
