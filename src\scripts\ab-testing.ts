// A/B Testing Framework
// Simple framework for testing different content approaches

interface ABTest {
  id: string;
  name: string;
  variants: ABVariant[];
  traffic: number; // Percentage of traffic to include (0-100)
  active: boolean;
  startDate: string;
  endDate?: string;
}

interface ABVariant {
  id: string;
  name: string;
  weight: number; // Percentage of test traffic (0-100)
  changes: ABChange[];
}

interface ABChange {
  selector: string;
  property: 'textContent' | 'innerHTML' | 'className' | 'style' | 'attribute';
  value: string;
  attributeName?: string;
}

class ABTestingFramework {
  private tests: ABTest[] = [];
  private userVariants: Map<string, string> = new Map();
  private userId: string;

  constructor() {
    this.userId = this.getUserId();
    this.loadUserVariants();
    this.initializeTests();
  }

  private getUserId(): string {
    let userId = localStorage.getItem('mpp_user_id');
    if (!userId) {
      userId = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      localStorage.setItem('mpp_user_id', userId);
    }
    return userId;
  }

  private loadUserVariants() {
    const stored = localStorage.getItem('mpp_ab_variants');
    if (stored) {
      try {
        const variants = JSON.parse(stored);
        this.userVariants = new Map(Object.entries(variants));
      } catch (error) {
        console.error('Failed to load A/B test variants:', error);
      }
    }
  }

  private saveUserVariants() {
    const variants = Object.fromEntries(this.userVariants);
    localStorage.setItem('mpp_ab_variants', JSON.stringify(variants));
  }

  private initializeTests() {
    // Define A/B tests for word wall reduction
    this.tests = [
      {
        id: 'package_description_length',
        name: 'Package Description Length Test',
        variants: [
          {
            id: 'control',
            name: 'Current Short Descriptions',
            weight: 50,
            changes: []
          },
          {
            id: 'ultra_short',
            name: 'Ultra Short Descriptions (3-4 words)',
            weight: 50,
            changes: [
              {
                selector: '[data-package-id="harvest-essentials"] .package-description',
                property: 'textContent',
                value: 'Cozy perfection'
              },
              {
                selector: '[data-package-id="pumpkin-charm"] .package-description',
                property: 'textContent',
                value: 'Color magic'
              },
              {
                selector: '[data-package-id="fall-favorites"] .package-description',
                property: 'textContent',
                value: 'Most popular'
              }
            ]
          }
        ],
        traffic: 100,
        active: true,
        startDate: new Date().toISOString()
      },
      {
        id: 'feature_display_format',
        name: 'Feature Display Format Test',
        variants: [
          {
            id: 'control',
            name: 'Categorized Features',
            weight: 50,
            changes: []
          },
          {
            id: 'simple_list',
            name: 'Simple Feature List',
            weight: 50,
            changes: [
              {
                selector: '.categorized-features',
                property: 'className',
                value: 'simple-features-list'
              }
            ]
          }
        ],
        traffic: 50,
        active: false, // Disabled for now
        startDate: new Date().toISOString()
      }
    ];

    this.runActiveTests();
  }

  private runActiveTests() {
    this.tests.forEach(test => {
      if (test.active && this.shouldIncludeInTest(test)) {
        const variant = this.getVariantForUser(test);
        this.applyVariant(test, variant);
        this.trackTestParticipation(test.id, variant.id);
      }
    });
  }

  private shouldIncludeInTest(test: ABTest): boolean {
    // Simple hash-based traffic allocation
    const hash = this.hashString(this.userId + test.id);
    const allocation = hash % 100;
    return allocation < test.traffic;
  }

  private getVariantForUser(test: ABTest): ABVariant {
    const existingVariant = this.userVariants.get(test.id);
    
    if (existingVariant) {
      const variant = test.variants.find(v => v.id === existingVariant);
      if (variant) return variant;
    }

    // Assign new variant based on weights
    const hash = this.hashString(this.userId + test.id + 'variant');
    const allocation = hash % 100;
    
    let cumulative = 0;
    for (const variant of test.variants) {
      cumulative += variant.weight;
      if (allocation < cumulative) {
        this.userVariants.set(test.id, variant.id);
        this.saveUserVariants();
        return variant;
      }
    }

    // Fallback to first variant
    const fallback = test.variants[0];
    this.userVariants.set(test.id, fallback.id);
    this.saveUserVariants();
    return fallback;
  }

  private applyVariant(test: ABTest, variant: ABVariant) {
    if (variant.id === 'control') return; // No changes for control

    variant.changes.forEach(change => {
      const elements = document.querySelectorAll(change.selector);
      elements.forEach(element => {
        this.applyChange(element as HTMLElement, change);
      });
    });

    console.log(`🧪 Applied A/B test variant: ${test.name} - ${variant.name}`);
  }

  private applyChange(element: HTMLElement, change: ABChange) {
    switch (change.property) {
      case 'textContent':
        element.textContent = change.value;
        break;
      case 'innerHTML':
        element.innerHTML = change.value;
        break;
      case 'className':
        element.className = change.value;
        break;
      case 'style':
        element.style.cssText = change.value;
        break;
      case 'attribute':
        if (change.attributeName) {
          element.setAttribute(change.attributeName, change.value);
        }
        break;
    }
  }

  private hashString(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }

  private trackTestParticipation(testId: string, variantId: string) {
    // Track participation in analytics
    if ((window as any).mppAnalytics) {
      (window as any).mppAnalytics.track({
        event: 'ab_test_participation',
        category: 'experimentation',
        action: 'test_assigned',
        label: `${testId}:${variantId}`,
        customData: {
          testId,
          variantId,
          userId: this.userId
        }
      });
    }
  }

  public trackConversion(testId: string, conversionType: string, value?: number) {
    const variantId = this.userVariants.get(testId);
    if (!variantId) return;

    if ((window as any).mppAnalytics) {
      (window as any).mppAnalytics.track({
        event: 'ab_test_conversion',
        category: 'experimentation',
        action: 'conversion',
        label: `${testId}:${variantId}:${conversionType}`,
        value,
        customData: {
          testId,
          variantId,
          conversionType,
          userId: this.userId
        }
      });
    }
  }

  public getActiveTests(): ABTest[] {
    return this.tests.filter(test => test.active);
  }

  public getUserVariant(testId: string): string | undefined {
    return this.userVariants.get(testId);
  }
}

// Initialize A/B testing framework
const abTesting = new ABTestingFramework();

// Export for global use
(window as any).mppABTesting = abTesting;

export default abTesting;
